{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "RBIRSh2FlYbT"}, "outputs": [], "source": ["# !pip install --upgrade pip\n", "# !pip install langchain\n", "# !pip install langchain_chroma\n", "# !pip install langchain_openai\n", "# !pop install dotenv"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Save the embeddings and chunk documents into separate files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "from dotenv import load_dotenv\n", "import time\n", "import os\n", "import json\n", "from langchain_openai import AzureOpenAIEmbeddings\n", "from os import listdir\n", "from os.path import isfile, join\n", "\n", "# Load the .env file\n", "load_dotenv()\n", "os.environ['AZURE_OPENAI_ENDPOINT'] = os.getenv(\"AZURE_OPENAI_EMBEDDING_ENDPOINT\")\n", "os.environ['OPENAI_API_VERSION'] = '2023-05-15'\n", "embeddings = AzureOpenAIEmbeddings(model='text-embedding-3-small')\n", "\n", "class Document:\n", "    def __init__(self, id, page_content, metadata):\n", "        self.id = id\n", "        self.page_content = page_content\n", "        self.metadata = metadata\n", "\n", "# Set up exponential backoff parameters\n", "def retry_with_backoff(api_call, *args, max_retries=5, initial_wait=8, **kwargs):\n", "    retries = 0\n", "    wait_time = initial_wait\n", "    while retries < max_retries:\n", "        try:\n", "            return api_call(*args, **kwargs)\n", "        except Exception as e:\n", "            print(f\"Rate limit exceeded. Retrying in {wait_time} seconds...\")\n", "            time.sleep(wait_time)\n", "            retries += 1\n", "            wait_time *= 2  # Exponential backoff\n", "            \n", "    raise Exception(\"Max retries exceeded\")\n", "\n", "def embed_and_store_text_chunks(file_paths, output_dir='./embedding_files'):\n", "    embedding_processed_files = \"embedding_processed_files.txt\"\n", "\n", "    if not os.path.exists(embedding_processed_files):\n", "        with open(embedding_processed_files, 'w') as file:\n", "            pass\n", "\n", "    with open(embedding_processed_files, \"r\") as file:\n", "        embedding_processed_file_paths = file.read()\n", "\n", "    os.makedirs(output_dir, exist_ok=True)\n", "\n", "    for file_path in file_paths:\n", "        if file_path in embedding_processed_file_paths:\n", "            continue\n", "        \n", "        print(f\"Processing File: {file_path}\")\n", "        with open(file_path, \"r\", encoding='utf-8') as f:\n", "            text = f.read()\n", "\n", "        # Split text into chunks\n", "        split_text = text.split('page_content')\n", "        split_text = [part.strip()[2:-1] for part in split_text[1:]]\n", "\n", "        cleaned_path = os.path.basename(file_path).replace(\".txt\", \"\")\n", "\n", "        # Convert to Document formatted chunks\n", "        chunk_docs = [Document(id=f\"{cleaned_path}_{i}\", page_content=chunk, metadata={\"source\": cleaned_path}) for i, chunk in enumerate(split_text)]\n", "\n", "        # Embed each chunk with exponential backoff\n", "        _embeddings = []\n", "        for chunk in split_text:\n", "            embedding_result = retry_with_backoff(lambda: embeddings.embed_documents([chunk]))\n", "            _embeddings.extend(embedding_result)\n", "\n", "        # Save the chunks and embeddings as separate JSON files\n", "        chunks_output_path = os.path.join(output_dir, f\"{cleaned_path}_chunks.json\")\n", "        embeddings_output_path = os.path.join(output_dir, f\"{cleaned_path}_embeddings.json\")\n", "\n", "        with open(chunks_output_path, 'w', encoding='utf-8') as chunk_file:\n", "            json.dump([doc.__dict__ for doc in chunk_docs], chunk_file, indent=4)\n", "\n", "        with open(embeddings_output_path, 'w', encoding='utf-8') as embedding_file:\n", "            json.dump(_embeddings, embedding_file, indent=4)\n", "\n", "        with open(embedding_processed_files, 'a', encoding='utf-8') as file:\n", "            file.write(f\"{file_path}\\n\")\n", "\n", "        print(f\"Processed and saved: {file_path}\")\n", "\n", "file_paths = []\n", "for f in listdir(\"./contextualized_chunks_files\"):\n", "   if isfile(join(\"./contextualized_chunks_files\", f)):\n", "        file_paths.append(join(\"./contextualized_chunks_files\", f))\n", "\n", "if __name__ == '__main__': \n", "    embed_and_store_text_chunks(file_paths)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Read the embedding and chunk document files back and store it into the Chroma database"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vJDOkpdHOBJb", "outputId": "9418e0a7-3262-4334-9fbe-2995fd74e6d2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully added BTSQC09.05.005 v4 to the Chroma database.\n", "Successfully added BTSQC11.01.WI002 v2 to the Chroma database.\n", "Successfully added BTSQC02.01.GL001 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.GL019 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.021 v1 to the Chroma database.\n", "Successfully added BTSQC19.12 v3 to the Chroma database.\n", "Successfully added BTSQC08.01.004 v2 to the Chroma database.\n", "Successfully added BTSQC09.05.006 v5 to the Chroma database.\n", "Successfully added BTSQC01.01 v5 to the Chroma database.\n", "Successfully added BTSQC08.01.002 v2 to the Chroma database.\n", "Successfully added BTSQC01-Q05 v4 to the Chroma database.\n", "Successfully added BTSQC09.05.002 v6 to the Chroma database.\n", "Successfully added BTSQC19.21 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.GL006 v3 to the Chroma database.\n", "Successfully added BTSQC11.01 v12-1 to the Chroma database.\n", "Successfully added BTSQC09.05.030 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.001 v8 to the Chroma database.\n", "Successfully added BTSQC09.05.015 v2 to the Chroma database.\n", "Successfully added BTSQC09.05.058 v2 to the Chroma database.\n", "Successfully added BTSQC00.01 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.GL014 v1 to the Chroma database.\n", "Successfully added BTSQC19.11.003 v1 to the Chroma database.\n", "Successfully added BTSQC05.01 v14 to the Chroma database.\n", "Successfully added BTSQC03.01 v8 to the Chroma database.\n", "Successfully added BTSQC19.19 v1 to the Chroma database.\n", "Successfully added BTSQC05.14 v3 to the Chroma database.\n", "Successfully added BTSQC19.21.001 v2 to the Chroma database.\n", "Successfully added BTSQC09.05.003 v1 to the Chroma database.\n", "Successfully added BTSQC19.12.002 v1 to the Chroma database.\n", "Successfully added BTSQC19.12.WI002 v2 to the Chroma database.\n", "Successfully added BTSQC01-Q06 v1 to the Chroma database.\n", "Successfully added BTSQC19.14.008 v3 to the Chroma database.\n", "Successfully added 4-023-005 v1 to the Chroma database.\n", "Successfully added BTSQC11.02.004 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.004 v4 to the Chroma database.\n", "Successfully added BTSQC00-Q01 v13 to the Chroma database.\n", "Successfully added BTSQC05.01.004 v11 to the Chroma database.\n", "Successfully added BTSQC05.11.002 v1 to the Chroma database.\n", "Successfully added BTSQC19.14.005 v3 to the Chroma database.\n", "Successfully added BTSQC09.05.025 v2 to the Chroma database.\n", "Successfully added BTSQC11.01 v12 to the Chroma database.\n", "Successfully added BTSQC09.05.013 v2 to the Chroma database.\n", "Successfully added BTSQC09.05.057 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.023 v1 to the Chroma database.\n", "Successfully added BTSQC00 v5 to the Chroma database.\n", "Successfully added BTSQC11.02.002 v2 to the Chroma database.\n", "Successfully added BTSQC11.02.008 v1 to the Chroma database.\n", "Successfully added BTSQC08.01.003 v2 to the Chroma database.\n", "Successfully added BTSQC11.02.006 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.022 v1 to the Chroma database.\n", "Successfully added BTSQC00.02 v1 to the Chroma database.\n", "Successfully added BTSQC08.05.001 v1 to the Chroma database.\n", "Successfully added BTSQC19.15 v1 to the Chroma database.\n", "Successfully added BTSQC11.02.005 v1 to the Chroma database.\n", "Successfully added BTSQC19.14.001 v3 to the Chroma database.\n", "Successfully added BTSQC09.05.026 v1 to the Chroma database.\n", "Successfully added BTSQC03.01.003 v12 to the Chroma database.\n", "Successfully added BTSQC19.15.WI001 v2 to the Chroma database.\n", "Successfully added BTSQC19.14.007 v3 to the Chroma database.\n", "Successfully added BTSQC19.15.WI004 v1 to the Chroma database.\n", "Successfully added BTSQC19.12.WI001 v1 to the Chroma database.\n", "Successfully added BTSQC19.12.001 v1 to the Chroma database.\n", "Successfully added BTSQC19.14.009 v3 to the Chroma database.\n", "Successfully added BTSQC01.03.001 v6 to the Chroma database.\n", "Successfully added BTSQC03.05 v4 to the Chroma database.\n", "Successfully added BTSQC19.14.006 v3 to the Chroma database.\n", "Successfully added BTSQC19.14.003 v3 to the Chroma database.\n", "Successfully added BTSQC08.03 v1 to the Chroma database.\n", "Successfully added BTSQC05.14.003 v1 to the Chroma database.\n", "Successfully added BTSQC05.14.001 v4 to the Chroma database.\n", "Successfully added BTSQC03.03 v3 to the Chroma database.\n", "Successfully added BTSQC19.11.001 v1 to the Chroma database.\n", "Successfully added BTSQC05.01.003 v11 to the Chroma database.\n", "Successfully added BTSQC03.01.004 v1 to the Chroma database.\n", "Successfully added BTSQC11.01.WI003 v2 to the Chroma database.\n", "Successfully added BTSQC19.14.004 v3 to the Chroma database.\n", "Successfully added BTSQC19.10.WI002 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.024 v2 to the Chroma database.\n", "Successfully added BTSQC01.02.GL001 v1 to the Chroma database.\n", "Successfully added BTSQC08.01 v4 to the Chroma database.\n", "Successfully added BTSQC09.40.005 v1 to the Chroma database.\n", "Successfully added BTSQC19.15.WI002 v1 to the Chroma database.\n", "Successfully added BTSQC11.02.007 v1 to the Chroma database.\n", "Successfully added BTSQC02.01.009 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.GL009 v1 to the Chroma database.\n", "Successfully added BTSQC11.02.003 v1 to the Chroma database.\n", "Successfully added BTSQC08.05 v2 to the Chroma database.\n", "Successfully added BTSQC00.03 v2 to the Chroma database.\n", "Successfully added BTSQC01.02 v9 to the Chroma database.\n", "Successfully added BTSQC19.16.WI002 v1 to the Chroma database.\n", "Successfully added 2-010-GN-001 v2 to the Chroma database.\n", "Successfully added BTSQC05.01.002 v3 to the Chroma database.\n", "Successfully added BTSQC11.02.001 v2 to the Chroma database.\n", "Successfully added BTSQC11.01.WI005 v2 to the Chroma database.\n", "Successfully added BTSQC19.19.GL001 v1 to the Chroma database.\n", "Successfully added BTSQC19.10.WI001 v1 to the Chroma database.\n", "Successfully added BTSQC19.15.WI003 v1 to the Chroma database.\n", "Successfully added BTSQC01.02.001 v1 to the Chroma database.\n", "Successfully added BTSQC19.12.004 v1 to the Chroma database.\n", "Successfully added BTSQC02.01.WI001 v4 to the Chroma database.\n", "Successfully added BTSQC02.01.008 v2 to the Chroma database.\n", "Successfully added BTSQC02.01.005 v1 to the Chroma database.\n", "Successfully added BTSQC02.01.GL003 v1 to the Chroma database.\n", "Successfully added BTSQC11.01.001 v14 to the Chroma database.\n", "Successfully added BTSQC19.12.003 v2 to the Chroma database.\n", "Successfully added BTSQC19.11 v2 to the Chroma database.\n", "Successfully added BTSQC11.01.WI001 v6 to the Chroma database.\n", "Successfully added BTSQC19.16 v1 to the Chroma database.\n", "Successfully added BTSQC05.14.GL002 v1 to the Chroma database.\n", "Successfully added BTSQC19.15.002 v2 to the Chroma database.\n", "Successfully added BTSQC19.10.GL001 v1 to the Chroma database.\n", "Successfully added BTSQC01-Q01 v9 to the Chroma database.\n", "Successfully added BTSQC09.05.019 v1 to the Chroma database.\n", "Successfully added BTSQC19.16.001 v1 to the Chroma database.\n", "Successfully added BTSQC09.05 v12 to the Chroma database.\n", "Successfully added BTSQC08.01.005 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.009 v2 to the Chroma database.\n", "Successfully added BTSQC02.01 v5 to the Chroma database.\n", "Successfully added BTSQC02.01.GL002 v1 to the Chroma database.\n", "Successfully added BTSQC05.14.002 v1 to the Chroma database.\n", "Successfully added BTSQC05.01.001 v15 to the Chroma database.\n", "Successfully added BTSQC11.02 v1 to the Chroma database.\n", "Successfully added BTSQC05.02.003 v6 to the Chroma database.\n", "Successfully added BTSQC19.14.010 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.008 v3 to the Chroma database.\n", "Successfully added BTSQC05.01.008 v5 to the Chroma database.\n", "Successfully added BTSQC05.02.004 v7 to the Chroma database.\n", "Successfully added BTSQC11.01.WI006 v2 to the Chroma database.\n", "Successfully added BTSQC19.15.001 v1 to the Chroma database.\n", "Successfully added BTSQC01.01.002 v6 to the Chroma database.\n", "Successfully added BTSQC09.05.007 v3 to the Chroma database.\n", "Successfully added BTSQC11.01.002 v3 to the Chroma database.\n", "Successfully added BTSQC11.01.WI007 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.020 v2 to the Chroma database.\n", "Successfully added BTSQC19.16.WI001 v1 to the Chroma database.\n", "Successfully added BTSQC05.11.001 v4 to the Chroma database.\n", "Successfully added BTSQC09.05.031 v1 to the Chroma database.\n", "Successfully added BTSQC19.11.002 v1 to the Chroma database.\n", "Successfully added BTSQC05.01.005 v3 to the Chroma database.\n", "Successfully added BTSQC02.01.006 v1 to the Chroma database.\n", "Successfully added BTSQC09.05.059 v1 to the Chroma database.\n", "Successfully added BTSQC03.01.001 v6 to the Chroma database.\n", "Successfully added BTSQC09.40.006 v2 to the Chroma database.\n", "Successfully added BTSQC19.14.002 v3 to the Chroma database.\n", "Successfully added BTSQC08.03.001 v1 to the Chroma database.\n", "Successfully added BTSQC05.14.GL001 v1 to the Chroma database.\n", "Successfully added BTSQC19.14 v3 to the Chroma database.\n"]}], "source": ["import os\n", "import json\n", "from langchain_chroma import Chroma\n", "from langchain_openai import AzureOpenAIEmbeddings\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "os.environ['AZURE_OPENAI_ENDPOINT'] = os.getenv(\"AZURE_OPENAI_EMBEDDING_ENDPOINT\")\n", "os.environ['OPENAI_API_VERSION'] = '2023-05-15'\n", "os.environ['AZURE_OPENAI_API_KEY'] = os.getenv(\"AZURE_OPENAI_API_KEY\")\n", "embeddings = AzureOpenAIEmbeddings(model='text-embedding-3-small')\n", "\n", "class Document:\n", "    def __init__(self, id, page_content, metadata):\n", "        self.id = id\n", "        self.page_content = page_content\n", "        self.metadata = metadata\n", "\n", "    def to_dict(self):\n", "        return {\n", "            \"id\": self.id,\n", "            \"page_content\": self.page_content,\n", "            \"metadata\": self.metadata\n", "        }\n", "\n", "    @staticmethod\n", "    def from_dict(data):\n", "        return Document(\n", "            id=data['id'],\n", "            page_content=data['page_content'],\n", "            metadata=data['metadata']\n", "        )\n", "\n", "# Initialize the embeddings and Chroma database\n", "embeddings = AzureOpenAIEmbeddings(model='text-embedding-3-small')\n", "vector_store = Chroma(\n", "    collection_name='bts-slc-db',\n", "    embedding_function=embeddings,\n", "    persist_directory='./chroma_db_new'\n", ")\n", "\n", "def load_and_store_chunks(output_dir='./embedding_files'):\n", "    for file_name in os.listdir(output_dir):\n", "        if file_name.endswith('_chunks.json'):\n", "            base_name = file_name.replace('_chunks.json', '')\n", "            chunks_path = os.path.join(output_dir, file_name)\n", "            embeddings_path = os.path.join(output_dir, f\"{base_name}_embeddings.json\")\n", "\n", "            if not os.path.exists(embeddings_path):\n", "                print(f\"Embeddings file not found for {file_name}. Skipping.\")\n", "                continue\n", "\n", "            # Load chunk documents\n", "            with open(chunks_path, 'r', encoding='utf-8') as chunk_file:\n", "                chunk_docs = [Document.from_dict(doc) for doc in json.load(chunk_file)]\n", "\n", "            # Load embeddings\n", "            with open(embeddings_path, 'r', encoding='utf-8') as embedding_file:\n", "                embeddings_data = json.load(embedding_file)\n", "\n", "            # Add to Chroma database\n", "            try:\n", "                vector_store.add_documents(documents=chunk_docs, embeddings=embeddings_data)\n", "                print(f\"Successfully added {base_name} to the Chroma database.\")\n", "            except Exception as e:\n", "                print(f\"Failed to add {base_name} to the Chroma database: {e}\")\n", "\n", "if __name__ == '__main__':\n", "    load_and_store_chunks()"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}