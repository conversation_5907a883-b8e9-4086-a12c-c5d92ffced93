from chains import *
from dotenv import load_dotenv
from retrieve import create_retriever
load_dotenv()

retrieval_grader = create_grade_docs_chain()
rag_chain = create_rag_chain()
question_rewriter = create_rewrite_query_chain()
hallucination_grader = create_hallucination_grader()
answer_grader = create_answer_grader()
retriever = create_retriever(chroma_path='./chroma_db')

# Function to set the initial Variables
def set_initial_vars(state):
    """
    Initializes the state with default values for retry counters.

    Args:
        state (dict): The initial state dictionary.

    Returns:
        dict: Updated state with `max_retries_q` and `max_retries_gen` initialized to 0.
    """
    print('---SETTING UP INITIAL VARIABLES---')
    max_retries_q = 0
    max_retries_gen = 0
    return {"max_retries_q": max_retries_q, "max_retries_gen": max_retries_gen}

# Function to retrieve documents
def retrieve(state):
    """
    Retrieves relevant documents for the query using a retriever.

    Args:
        state (dict): The current state containing the query.

    Returns:
        dict: Updated state with retrieved documents, their sources, and the incremented retry count.
    """
    print("---RETRIEVING DOCUMENTS---")
    query = state['query']
    max_retries_q = state['max_retries_q']
    max_retries_q += 1

    # Retrieve from Vector db and BM25 Keyword Search
    res_retrieve, source_res = retriever.search(query=query, k=3, bm_search=True)
    documents = []
    document_sources = []
    for res in res_retrieve: 
        documents.append(res[0])
    for source in source_res:
        document_sources.append(source[0])
    print(len(documents))
    return {"documents": documents, "query": query, 
            "document_sources": document_sources, "max_retries_q": max_retries_q}

# Function to grade the documents (Check if retrieved documents are relevant to the query)
def grade_document(state):
    """
    Grades the relevance of retrieved documents to the query.

    Args:
        state (dict): The current state containing the query and retrieved documents.

    Returns:
        dict: Updated state with a filtered list of relevant documents.
    """
    print("---CHECK DOCUMENT RELEVANCE TO QUERY---")
    query = state['query']
    documents = state['documents']

    # Grade each document/chunk
    filtered_docs = []
    for d in documents:
        score = retrieval_grader.invoke(
            {"question": query, "document": d}
        )
        grade = score.binary_score
        if grade == "yes":
            print("---GRADE: DOCUMENT/CHUNK RELEVANT---")
            filtered_docs.append(d)
        else:
            print("---GRADE: DOCUMENT/CHUNK NOT RELEVANT---")
            continue
        # print(len(filtered_docs))
    return {"documents": filtered_docs, "query": query}
    
# Generate a response based on the retrieved documents
def generate(state):
    """
    Generates a response based on the query and retrieved documents.

    Args:
        state (dict): The current state containing the query and filtered documents.

    Returns:
        dict: Updated state with the generated response and incremented generation retry count.
    """
    print("---GENERATING RESPONSE---")
    query = state['query']
    documents = state['documents']
    max_retries_gen = state['max_retries_gen']
    max_retries_gen += 1

    generation = rag_chain.invoke(
        {"context": documents, "question": query}
    )

    if max_retries_gen == 3:
        msg = """This answer may not fully address the question and could contain inaccuracies. Please refer to the retrieved documents for a more precise and detailed response."""
        return {"generation": generation, "max_retries_gen": max_retries_gen, "msg": msg}

    return {"generation": generation, "max_retries_gen": max_retries_gen}

# Function to rewrite the user query for better retrieval
def rewrite_query(state):
    """
    Rewrites the query to improve retrieval performance.

    Args:
        state (dict): The current state containing the query.

    Returns:
        dict: Updated state with the rewritten query.
    """
    print("---REWRITING QUERY---")
    query = state['query']

    # Rewrite the user query
    better_query = question_rewriter.invoke({"question": query})
    return {"query": query}


# Placeholder for query decomposition
def query_decomposition(state):
    """
    Placeholder for query decomposition logic. Splits the query into subqueries.

    Args:
        state (dict): The current state containing the query.

    Returns:
        dict: Updated state with the decomposed query.
    """
    print("---QUERY DECOMPOSITION---")
    query = state['query']
    # Logic goes here
    return {"query": query}


### TODO: Function for RAG FUSION

# Edges
# Proceed to generation if there are 2 or more relevant docs (if not we will route to the relevant query rewrite method)
def decide_to_generate(state):
    """
    Determines whether to proceed to response generation or query rewrite based on the number of relevant documents.

    Args:
        state (dict): The current state containing the query, documents, and retry count.

    Returns:
        str: The next step ('generate', 'rewrite_query', or 'end').
    """
    print("---ASSESS GRADED DOCUMENTS---")
    query = state['query']
    rel_docs = len(state['documents'])
    # Get the max retries to avoid loops
    max_retries_q = state['max_retries_q']

    if rel_docs >= 1:
        print("---DECISION: GENERATE---")
        return "generate"
    else:
        print("---DECISION: QUERY REWRITE---")
        # Invoke the Retrieval router
        # route = retrieval_router.invoke({"question": query})
        # decision_route = route.retrieval_method
        # if decision_route == 'rewrite_query':
        #     print("---DECISION: REWRITE QUERY---")
        #     return "rewrite_query"
        # else:
        #     print("---DECISION: QUERY DECOMPOSITION---")
        #     return "query_decomposition"
        if max_retries_q < 3:
            return "rewrite_query"

        else:
            print("---EXCEEDED MAX TRIES FOR QUERY: ENDING....---")
            return "end"
        
def grade_generation(state):
    """
    Grades the generated response for hallucinations and question relevance.

    Args:
        state (dict): The current state containing the query, documents, and generated response.

    Returns:
        str: The next step ('useful', 'not useful', 'not supported', or 'end').
    """
    print("---CHECK HALLUCINATIONS---")
    query = state['query']
    documents = state['documents']
    generation = state['generation']
    # for testing the loops
    # generation = "Hello!"
    max_retries_gen = state['max_retries_gen']

    if max_retries_gen >= 3:
        print("---EXCEEDED MAX RETRIES FOR GENERATION: ENDING...---")
        return "end" 
    score = hallucination_grader.invoke(
        {"documents": documents, "generation": generation}
    )
    grade = score.binary_score


    # Check Hallucinations
    if grade == 'yes':
        print("---DECISION: GENERATION IS GROUNDED IN DOCUMENTS---")
        # Check question-answering
        print("---GRADE GENERATION vs QUESTION---")
        score = answer_grader.invoke({"question": query, "generation": generation})
        grade = score.binary_score
        if grade == "yes":
            print("---DECISION: GENERATION ADDRESSES QUESTION---")
            return "useful"
        else:
            print("---DECISION: GENERATION DOES NOT ADDRESS QUESTION---")
            return "not useful"
    else:
        print("---DECISION: GENERATION IS NOT GROUNDED IN DOCUMENTS, RE-TRY---")
        return "not supported"