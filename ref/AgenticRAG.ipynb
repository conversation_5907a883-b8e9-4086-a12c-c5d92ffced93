{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "from utils import * \n", "from bm25retrieve import BM25Retriever\n", "from typing import Literal\n", "from langchain_core.pydantic_v1 import BaseModel, Field\n", "from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain import hub\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_chroma import Chroma\n", "from retrieve import RetrieveVectorDbWithBM25\n", "from os import listdir\n", "from os.path import isfile, join\n", "from langchain_core.documents import Document\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text_dict = load_text_files(input_folder='./text_files/')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(text_dict)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Retrievers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup the embedding model\n", "os.environ['AZURE_OPENAI_ENDPOINT'] = os.getenv(\"AZURE_OPENAI_EMBEDDING_ENDPOINT\")\n", "os.environ['OPENAI_API_VERSION'] = os.getenv(\"AZURE_EMBEDDING_API_VERSION\")\n", "embeddings = AzureOpenAIEmbeddings(model='text-embedding-3-small')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vector_store = Chroma(\n", "    embedding_function=embeddings,\n", "    persist_directory='./chroma_db_new1',\n", "    collection_name='bts-slc-db'\n", ")\n", "\n", "# vector_store.get()['ids']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"What is Free and Open-Source Software?\"\n", "docs = vector_store.similarity_search(query, k=3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["docs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get the chunks\n", "file_paths = []\n", "for f in listdir(\"contextualized_chunks_files\"):\n", "   if isfile((join(\"./contextualized_chunks_files\", f))):\n", "        file_paths.append(\"./contextualized_chunks_files/\" + f)\n", "\n", "chunks = []\n", "\n", "for file_path in file_paths:\n", "    with open(file_path, \"r\", encoding='utf-8') as f:\n", "        text = f.read()\n", "\n", "    split_text = text.split('page_content')\n", "\n", "    # Remove any leading or trailing whitespace from each split part\n", "    split_text = [part.strip()[2:-1] for part in split_text[1:]]\n", "\n", "    cleaned_path = file_path.replace(\"./contextualized_chunks_files/\", \"\")\n", "    \n", "    # Convert to Document formatted chunks\n", "    for txt in split_text:\n", "        document = Document(\n", "            page_content=txt,\n", "            metadata = {\"source\": cleaned_path}\n", "        )\n", "        chunks.append(document)\n", "# Create the BM25 Retriever\n", "bm25_retriever = BM25Retriever(chunks)\n", "\n", "# Test out the retriever\n", "query = \"What is Free and Open-Source Software?\"\n", "relevant_docs, scores = bm25_retriever.get_relevant_documents(query, k=3)\n", "relevant_docs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["relevant_docs[0].page_content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup the retriever\n", "from retrieve import RetrieveVectorDbWithBM25\n", "\n", "retriever = RetrieveVectorDbWithBM25(vector_db=vector_store, bm25_retriever=bm25_retriever)\n", "retriever.search(query=query, k=3, bm_search=True)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### LLM Chains"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Retrieval Grader\n", "# Data model\n", "class GradeDocuments(BaseModel):\n", "    \"\"\"Binary score for relevance check on retrieved documents.\"\"\"\n", "\n", "    binary_score: str = Field(\n", "        description=\"Documents are relevant to the question, 'yes' or 'no'\"\n", "    )\n", "\n", "# LLM with function call\n", "llm = AzureChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "structured_llm_grader = llm.with_structured_output(GradeDocuments)\n", "\n", "# Prompt\n", "system = \"\"\"You are a grader assessing relevance of a retrieved document to a user question. \\n \n", "    If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant. \\n\n", "    It does not need to be a stringent test. The goal is to filter out erroneous retrievals. \\n\n", "    Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.\"\"\"\n", "grade_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"Retrieved documents: \\n\\n {document} \\n\\n User question: {question}\"),\n", "    ]\n", ")\n", "\n", "retrieval_grader = grade_prompt | structured_llm_grader\n", "query = \"What is Free and Open-Source Software?\"\n", "# docs, _ = bm25_retriever.get_relevant_documents(query, k=2)\n", "# doc_txt = docs[1].page_content\n", "res_retrieve = retriever.search(query=query, k=3, bm_search=True)\n", "docs = []\n", "for res in res_retrieve: \n", "    docs.append(res[0])\n", "print(retrieval_grader.invoke({\"question\": query, \"document\": docs[3]}))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Generation \n", "\n", "# Prompt\n", "prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "# LLM\n", "llm = AzureChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "\n", "# Post-processing\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "\n", "# Chain\n", "rag_chain = prompt | llm | StrOutputParser()\n", "\n", "# Run\n", "generation = rag_chain.invoke({\"context\": docs, \"question\": query})\n", "print(generation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Hallucination Grader\n", "\n", "# Data model\n", "class GradeHallucinations(BaseModel):\n", "    \"\"\"Binary score for hallucination present in generation answer.\"\"\"\n", "\n", "    binary_score: str = Field(\n", "        description=\"Answer is grounded in the facts, 'yes' or 'no'\"\n", "    )\n", "\n", "\n", "# LLM with function call\n", "llm = AzureChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "structured_llm_grader = llm.with_structured_output(GradeHallucinations)\n", "\n", "# Prompt\n", "system = \"\"\"You are a grader assessing whether an LLM generation is grounded in / supported by a set of retrieved facts. \\n \n", "     Give a binary score 'yes' or 'no'. 'Yes' means that the answer is grounded in / supported by the set of facts.\"\"\"\n", "hallucination_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"Set of facts: \\n\\n {documents} \\n\\n LLM generation: {generation}\"),\n", "    ]\n", ")\n", "\n", "hallucination_grader = hallucination_prompt | structured_llm_grader\n", "hallucination_grader.invoke({\"documents\": docs, \"generation\": generation})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Answer Grader\n", "\n", "# Data model\n", "class GradeAnswer(BaseModel):\n", "    \"\"\"Binary score to assess answer addresses question.\"\"\"\n", "\n", "    binary_score: str = Field(\n", "        description=\"Answer addresses the question, 'yes' or 'no'\"\n", "    )\n", "\n", "\n", "# LLM with function call\n", "llm = AzureChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "structured_llm_grader = llm.with_structured_output(GradeAnswer)\n", "\n", "# Prompt\n", "system = \"\"\"You are a grader assessing whether an answer addresses / resolves a question \\n \n", "     Give a binary score 'yes' or 'no'. Yes' means that the answer resolves the question.\"\"\"\n", "answer_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"User question: \\n\\n {question} \\n\\n LLM generation: {generation}\"),\n", "    ]\n", ")\n", "\n", "answer_grader = answer_prompt | structured_llm_grader\n", "answer_grader.invoke({\"question\": query, \"generation\": generation})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Retriver method router\n", "\n", "# Data model\n", "class RouteRetrievalQuery(BaseModel):\n", "    \"\"\"Route a user query to the most relevant retrival method.\"\"\"\n", "\n", "    retrieval_method: Literal[\"rewrite_query\", \"query_decomposition\"] = Field(\n", "        ...,\n", "        description=\"Given a user question choose to route it to rewrite_query or query_decomposition.\",\n", "    )\n", "\n", "\n", "# LLM with function call\n", "llm = AzureChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "structured_llm_router = llm.with_structured_output(RouteRetrievalQuery)\n", "\n", "# Prompt\n", "system = \"\"\"You are an expert at routing a user query to a rewrite_query or query_decomposition.\n", "Determine the best retrieval method for performing RAG by choosing \\\n", "between rewrite_query and query_decomposition. Use query_decomposition when the \\\n", "query can be broken down into multiple meaningful search queries. Otherwise, opt for rewrite_query.\"\"\"\n", "route_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"{question}\"),\n", "    ]\n", ")\n", "\n", "retrieval_router = route_prompt | structured_llm_router\n", "print(retrieval_router.invoke({\"question\": \"What is PCS Archer and what are its types?\"}))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Rewrite query\n", "\n", "# LLM\n", "llm = AzureChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "\n", "# Prompt\n", "system = \"\"\"You a question re-writer that converts an input question to a better version that is optimized \\n \n", "     for vectorstore retrieval. Look at the input and try to reason about the underlying semantic intent / meaning.\"\"\"\n", "re_write_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\n", "            \"human\",\n", "            \"Here is the initial question: \\n\\n {question} \\n Formulate an improved question.\",\n", "        ),\n", "    ]\n", ")\n", "\n", "question_rewriter = re_write_prompt | llm | StrOutputParser()\n", "question_rewriter.invoke({\"question\": query})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Query Decomposition\n", "\n", "# LLM\n", "llm = AzureChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "\n", "# Prompt\n", "system = \"\"\"\n", "You are a helpful assistant that generates multiple sub-questions related to an input question. \\n\n", "The goal is to break down the input into a set of sub-problems / sub-questions that can be answers in isolation. \\n\n", "Generate multiple search queries related to: {question} \\n\n", "Output (2 queries):\n", "\"\"\"\n", "re_write_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\n", "            \"human\",\n", "            \"Here is the initial question: \\n\\n {question} \\n Formulate improved questions.\",\n", "        ),\n", "    ]\n", ")\n", "\n", "question_rewriter = re_write_prompt | llm | StrOutputParser() | (lambda x: x.split(\"\\n\"))\n", "question_rewriter.invoke({\"question\": query})"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Building the Graph"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define the Graph state\n", "from typing_extensions import TypedDict\n", "from typing import List\n", "\n", "class GraphState(TypedDict):\n", "    query: str\n", "    generation: str\n", "    documents: List[str]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res_retrieve = retriever.search(query=query, k=3, bm_search=True)\n", "docs = []\n", "for res in res_retrieve: \n", "    docs.append(res[0])\n", "docs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.schema import Document\n", "\n", "# Function to retrieve documents\n", "def retrieve(state):\n", "    print(\"---RETRIEVING DOCUMENTS---\")\n", "    query = state['query']\n", "\n", "    # Retrieve from Vector db and BM25 Keyword Search\n", "    res_retrieve = retriever.search(query=query, k=3, bm_search=True)\n", "    documents = []\n", "    for res in res_retrieve: \n", "        documents.append(res[0])\n", "    # print(len(documents))\n", "    return {\"documents\": documents, \"query\": query}\n", "\n", "# Function to grade the documents (Check if retrieved documents are relevant to the query)\n", "def grade_document(state):\n", "    print(\"---<PERSON><PERSON><PERSON> DOCUMENT RELEVANCE TO QUERY---\")\n", "    query = state['query']\n", "    documents = state['documents']\n", "\n", "    # Grade each document/chunk\n", "    filtered_docs = []\n", "    for d in documents:\n", "        score = retrieval_grader.invoke(\n", "            {\"question\": query, \"document\": d}\n", "        )\n", "        grade = score.binary_score\n", "        if grade == \"yes\":\n", "            print(\"---GRADE: DOCUMENT/CHUNK RELEVANT---\")\n", "            filtered_docs.append(d)\n", "        else:\n", "            print(\"---GRADE: DOCUMENT/CHUNK NOT RELEVANT---\")\n", "            continue\n", "        # print(len(filtered_docs))\n", "    return {\"documents\": filtered_docs, \"query\": query}\n", "    \n", "# Generate a response based on the retrieved documents\n", "def generate(state):\n", "    print(\"---GENERATING RESPONSE---\")\n", "    query = state['query']\n", "    documents = state['documents']\n", "\n", "    generate = rag_chain.invoke(\n", "        {\"context\": documents, \"question\": query}\n", "    )\n", "\n", "    return {\"generation\": generation}\n", "\n", "# Function to rewrite the user query for better retrieval\n", "def rewrite_query(state):\n", "    print(\"---REWRITING QUERY---\")\n", "    query = state['query']\n", "    \n", "    # Rewrite the user query\n", "    better_query = question_rewriter.invoke({\"question\": query})\n", "    return {\"query\": query}\n", "\n", "# Placeholder for query decomposition\n", "def query_decomposition(state):\n", "    print(\"---QUERY DECOMPOSITION---\")\n", "    query = state['query']\n", "    # Logic goes here\n", "    return {\"query\": query}\n", "\n", "\n", "### TODO: Function for RAG FUSION\n", "\n", "# Edges\n", "# Proceed to generation if there are 2 or more relevant docs (if not we will route to the relevant query rewrite method)\n", "def decide_to_generate(state): \n", "    print(\"---ASSESS GRADED DOCUMENTS---\")\n", "    query = state['query']\n", "    rel_docs = len(state['documents'])\n", "\n", "    if rel_docs >= 2:\n", "        print(\"---DECISION: GENERATE---\")\n", "        return \"generate\"\n", "    else:\n", "        print(\"---DECISION: QUERY REWRITE---\")\n", "        # Invoke the Retrieval router\n", "        # route = retrieval_router.invoke({\"question\": query})\n", "        # decision_route = route.retrieval_method\n", "        # if decision_route == 'rewrite_query':\n", "        #     print(\"---DECISION: REWRITE QUERY---\")\n", "        #     return \"rewrite_query\"\n", "        # else:\n", "        #     print(\"---DECISION: QUERY DECOMPOSITION---\")\n", "        #     return \"query_decomposition\"\n", "        return \"rewrite_query\"\n", "        \n", "def grade_generation(state):\n", "    print(\"---CHEC<PERSON> HALLUCINATIONS---\")\n", "    query = state['query']\n", "    documents = state['documents']\n", "    generation = state['generation']\n", "\n", "    score = hallucination_grader.invoke(\n", "        {\"documents\": documents, \"generation\": generation}\n", "    )\n", "    grade = score.binary_score\n", "\n", "    # Check Hallucinations\n", "    if grade == 'yes':\n", "        print(\"---DECISION: GENERATION IS GROUNDED IN DOCUMENTS---\")\n", "        # Check question-answering\n", "        print(\"---GRADE GENERATION vs QUESTION---\")\n", "        score = answer_grader.invoke({\"question\": query, \"generation\": generation})\n", "        grade = score.binary_score\n", "        if grade == \"yes\":\n", "            print(\"---DECISION: GENERATION ADDRESSES QUESTION---\")\n", "            return \"useful\"\n", "        else:\n", "            print(\"---DECISION: GENERATION DOES NOT ADDRESS QUESTION---\")\n", "            return \"not useful\"\n", "    else:\n", "        print(\"---DECISION: GENERATION IS NOT GROUNDED IN DOCUMENTS, RE-TRY---\")\n", "        return \"not supported\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph, START\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"retrieve\", retrieve)\n", "workflow.add_node(\"grade_document\", grade_document)\n", "workflow.add_node(\"generate\", generate)\n", "workflow.add_node(\"rewrite_query\", rewrite_query)\n", "# workflow.add_node(\"query_decomposition\", query_decomposition)\n", "\n", "# Build the Graph\n", "workflow.add_edge(START, \"retrieve\")\n", "workflow.add_edge(\"retrieve\", \"grade_document\")\n", "workflow.add_conditional_edges(\n", "    \"grade_document\", \n", "    decide_to_generate,\n", "    {\n", "        \"generate\": \"generate\",\n", "        \"rewrite_query\": \"rewrite_query\",       \n", "    }\n", ")\n", "# workflow.add_edge(\"query_decomposition\", \"retrieve\")\n", "workflow.add_edge(\"rewrite_query\", \"retrieve\")\n", "workflow.add_conditional_edges(\n", "    \"generate\", \n", "    grade_generation, \n", "    {\n", "        \"not supported\": \"generate\",\n", "        \"useful\": END,\n", "        \"not useful\": \"generate\"\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["app = workflow.compile()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display the graph\n", "from IPython.display import Image\n", "\n", "app.get_graph()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pprint import pprint\n", "\n", "inputs = {\n", "    \"query\": \"What is Free and Open-Source Software?\"\n", "}\n", "\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        pprint(f\"Node: '{key}': \")\n", "    pprint(\"\\n--\\n\")\n", "\n", "pprint(value['generation'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(value['generation'])"]}], "metadata": {"kernelspec": {"display_name": "slc-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}