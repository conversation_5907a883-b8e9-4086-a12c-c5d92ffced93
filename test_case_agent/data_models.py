# Imports
from pydantic import BaseModel, Field
from typing import List

# Pydantic for structured output
class TestCase(BaseModel):
    requirement_id: str = Field(description="Requirement Id from the csv")
    # requirement_description: str = Field(description="Requirement text from the csv")
    test_case_description: str = Field(description="Test case that validates the requirement")
    inputs: str = Field(description="List of inputs for the test case")
    expected_outcome: str = Field(description="Expected result of the test case")

class TestCases(BaseModel):
    test_cases: List[TestCase] = Field(description="A list of the test cases")

### Feedback Chain
class FeedbackTemplate(BaseModel):
    test_case_id: str = Field(description="The ID of the test case which is incorrect, incomplete, or contains inaccuracies or irrelevant details.")
    feedback: str = Field(description="Detailed feedback identifying the issues in the test case, including what is incorrect, missing, or needs modification.")

class Feedback(BaseModel):
    feedback_list: List[FeedbackTemplate] = Field(description="A list of feedback entries, each identifying issues in specific test cases that are incorrect, incomplete, or contain inaccuracies. Each entry includes the requirement ID and corresponding feedback details.")

# Pydantic for structured output
class CorrectedTestCase(BaseModel):
    test_case_id: str = Field(description="test case Id from the feedback")
    test_case_description: str = Field(description="Test case that validates the requirement and the feedback")
    inputs: str = Field(description="List of inputs for the test case")
    expected_outcome: str = Field(description="Expected result of the test case")

class CorrectedTestCases(BaseModel):
    test_cases: List[CorrectedTestCase] = Field(description="A list of the test cases")

class GenTestCase(BaseModel):
    requirement_id: str = Field(description="Requirement Id from the csv")
    test_case_description: str = Field(description="Test case that validates the requirement")
    inputs: str = Field(description="List of inputs for the test case")
    expected_outcome: str = Field(description="Expected result of the test case")
    test_type: str = Field(description="Type of the test case: positive, negative, or edge")  # New field

class GenTestCases(BaseModel):
    test_cases: List[GenTestCase] = Field(description="A list of the test cases")
