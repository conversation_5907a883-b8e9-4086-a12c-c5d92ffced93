import pandas as pd
from pprint import pprint
from graph import create_graph_workflow
from dotenv import load_dotenv
import json
import os

def generate_test_cases(input_filepath, output_filepath, print_res=True):
    # Create the app
    generator = create_graph_workflow()
    # Define the inputs  
    inputs = {"csv_file_path": input_filepath, 'output_file_path': output_filepath}
    if print_res == True:
        for output in generator.stream(inputs):
            for key, value in output.items():
                # Node
                pprint(f"Node '{key}':")
            pprint("\n---\n")
    else:
        for output in generator.stream(inputs):
            for key, value in output.items():
                pass
