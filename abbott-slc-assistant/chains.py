from langchain_openai import AzureChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain import hub
from langchain_core.output_parsers import StrOutputParser
from data_models import *
from dotenv import load_dotenv
import os

# Load the API keys
load_dotenv()

os.environ['AZURE_OPENAI_ENDPOINT'] = os.getenv("AZURE_OPENAI_ENDPOINT")
os.environ['OPENAI_API_VERSION'] = os.getenv("OPENAI_API_VERSION")

def create_grade_docs_chain():
    """
    Creates a chain to assess the relevance of retrieved documents to a user's query.

    The chain uses an AzureChatOpenAI model with structured output to generate a binary
    relevance grade ('yes' or 'no') for each document based on its relation to the query.

    Returns:
        Callable: A chain combining a structured LLM with a relevance-grading prompt.
    """

    # LLM with function call
    llm = AzureChatOpenAI(model="gpt-4o", temperature=0)
    structured_llm_grader = llm.with_structured_output(GradeDocuments)

    # Prompt
    system = """You are a grader assessing relevance of a retrieved document to a user question. \n 
        If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant. \n
        It does not need to be a stringent test. The goal is to filter out erroneous retrievals. \n
        Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question."""
    grade_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system),
            ("human", "Retrieved documents: \n\n {document} \n\n User question: {question}"),
        ]
    )

    retrieval_grader = grade_prompt | structured_llm_grader
    return retrieval_grader

def create_rag_chain():
    """
    Creates a Retrieval-Augmented Generation (RAG) chain for generating responses.

    The chain combines a predefined RAG prompt, an AzureChatOpenAI model, and a 
    string output parser to format the final response.

    Returns:
        Callable: A chain for retrieval-augmented generation.
    """

    # Prompt
    prompt = hub.pull("rlm/rag-prompt")

    # LLM
    llm = AzureChatOpenAI(model="gpt-4o", temperature=0)

    # Post-processing
    def format_docs(docs):
        """
        Formats documents into a single string with each document separated by newlines.

        Args:
            docs (list): A list of document objects with a `page_content` attribute.

        Returns:
            str: Formatted string combining the contents of all documents.
        """
        return "\n\n".join(doc.page_content for doc in docs)


    # Chain
    rag_chain = prompt | llm | StrOutputParser()
    return rag_chain

def create_hallucination_grader():
    """
    Creates a chain to evaluate whether LLM-generated responses are grounded in provided facts.

    The chain uses an AzureChatOpenAI model with structured output to grade the grounding of
    responses with a binary score ('yes' or 'no').

    Returns:
        Callable: A chain for hallucination grading.
    """

    # LLM with function call
    llm = AzureChatOpenAI(model="gpt-4o", temperature=0)
    structured_llm_grader = llm.with_structured_output(GradeHallucinations)

    # Prompt
    system = """You are a grader assessing whether an LLM generation is grounded in / supported by a set of retrieved facts. \n 
        Give a binary score 'yes' or 'no'. 'Yes' means that the answer is grounded in / supported by the set of facts."""
    hallucination_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system),
            ("human", "Set of facts: \n\n {documents} \n\n LLM generation: {generation}"),
        ]
    )

    hallucination_grader = hallucination_prompt | structured_llm_grader
    return hallucination_grader

def create_answer_grader():
    """
    Creates a chain to evaluate whether an LLM-generated answer adequately addresses a question.

    The chain uses an AzureChatOpenAI model with structured output to grade the answer with a binary score ('yes' or 'no').

    Returns:
        Callable: A chain for answer grading.
    """

    # LLM with function call
    llm = AzureChatOpenAI(model="gpt-4o", temperature=0)
    structured_llm_grader = llm.with_structured_output(GradeAnswer)

    # Prompt
    system = """You are a grader assessing whether an answer addresses / resolves a question \n 
        Give a binary score 'yes' or 'no'. Yes' means that the answer resolves the question."""
    answer_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system),
            ("human", "User question: \n\n {question} \n\n LLM generation: {generation}"),
        ]
    )

    answer_grader = answer_prompt | structured_llm_grader
    return answer_grader

def create_rewrite_query_chain():
    """
    Creates a chain to rewrite user questions for better optimization in vectorstore retrieval.

    The chain uses an AzureChatOpenAI model to generate a rewritten question based on the semantic intent
    of the input question.

    Returns:
        Callable: A chain for query rewriting.
    """
    
    # LLM
    llm = AzureChatOpenAI(model="gpt-4o", temperature=0)

    # Prompt
    system = """You a question re-writer that converts an input question to a better version that is optimized \n 
        for vectorstore retrieval. Look at the input and try to reason about the underlying semantic intent / meaning."""
    re_write_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system),
            (
                "human",
                "Here is the initial question: \n\n {question} \n Formulate an improved question.",
            ),
        ]
    )

    question_rewriter = re_write_prompt | llm | StrOutputParser()
    return question_rewriter

