<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Case Manager</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <div class="container">
        <h1>Test Case Manager</h1>

        <!-- Test Case Generator Section -->
        <div class="section">
            <h2>Test Case Generator</h2>
            <form id="upload-form" action="/generate_test_cases" method="post" enctype="multipart/form-data">
                <label for="file">Upload Requirements CSV:</label>
                <input type="file" id="file" name="file" accept=".csv" required>
                <button type="submit">Generate Test Cases</button>
            </form>
        </div>
        <p id="loading" style="display: none;">Generating test cases, please wait...</p>

        <!-- Test Case Updater Section -->
        <div class="section">
            <h2>Test Case Updater</h2>
            <form id="update-form" action="/update_test_cases" method="post" enctype="multipart/form-data">
                <label for="old-req-file">Upload Old Requirements File:</label>
                <input type="file" id="old-req-file" name="old_req_file" accept=".csv" required>

                <label for="new-req-file">Upload New Requirements File:</label>
                <input type="file" id="new-req-file" name="new_req_file" accept=".csv" required>

                <label for="old-test-cases-file">Upload Old Test Cases File:</label>
                <input type="file" id="old-test-cases-file" name="old_test_cases_file" accept=".csv" required>

                <button type="submit">Update Test Cases</button>
            </form>
        </div>

        <p id="updating" style="display: none;">Updating test cases, please wait...</p>
    </div>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>