# Imports
from typing_extensions import TypedDict
from langgraph.graph import <PERSON><PERSON>, StateGraph, START
from nodes import * 

# Define the Graph state
class GraphState(TypedDict):
    """
    Represents the state of the graph workflow for test case generation and feedback correction.

    Attributes:
        csv_file_path (str): Path to the input CSV file containing requirements.
        output_file_path (str): Path for the combined output CSV file.
        csv_file_contents (str): Contents of the CSV file.
        pos_csv_file_path (str): Path to the file containing positive test cases.
        neg_csv_file_path (str): Path to the file containing negative test cases.
        edge_csv_file_path (str): Path to the file containing edge test cases.
        pos_fb (str): Feedback for positive test cases.
        neg_fb (str): Feedback for negative test cases.
        edge_fb (str): Feedback for edge test cases.
        pos_test_cases_fb (str): Corrected positive test cases after feedback.
        neg_test_cases_fb (str): Corrected negative test cases after feedback.
        edge_test_cases_fb (str): Corrected edge test cases after feedback.
    """
    csv_file_path: str
    output_file_path: str
    csv_file_contents: str
    pos_csv_file_path: str
    neg_csv_file_path: str
    edge_csv_file_path: str
    pos_fb: str
    neg_fb: str
    edge_fb: str
    pos_test_cases_fb: str
    neg_test_cases_fb: str
    edge_test_cases_fb: str

def create_graph_workflow():
    """
    Creates the graph workflow for test case generation and feedback correction.

    The workflow includes the following steps:
        1. Load the CSV file with requirements.
        2. Generate positive, negative, and edge test cases.
        3. Get feedback for each type of test case.
        4. Correct test cases based on feedback.
        5. Combine all test cases into a single CSV file.

    Returns:
        Callable: The compiled graph workflow application.
    """
    workflow = StateGraph(GraphState)

    # Define the nodes
    workflow.add_node("load_csv", load_csv)
    workflow.add_node("generate_pos_test_cases", generate_pos_test_cases)
    workflow.add_node("generate_neg_test_cases", generate_neg_test_cases)
    workflow.add_node("generate_edge_test_cases", generate_edge_test_cases)
    workflow.add_node("get_pos_feedback", get_pos_feedback)
    workflow.add_node("get_neg_feedback", get_neg_feedback)
    workflow.add_node("get_edge_feedback", get_edge_feedback)
    workflow.add_node("correct_pos_tests", correct_pos_tests)
    workflow.add_node("correct_neg_tests", correct_neg_tests)
    workflow.add_node("correct_edge_tests", correct_edge_tests)
    workflow.add_node("combine_csv_files", combine_csv_files)

    # Build the graph
    workflow.add_edge(START, "load_csv")
    workflow.add_edge("load_csv", "generate_pos_test_cases")
    workflow.add_edge("load_csv", "generate_neg_test_cases")
    workflow.add_edge("load_csv", "generate_edge_test_cases")
    workflow.add_edge("generate_pos_test_cases", "get_pos_feedback")
    workflow.add_edge("generate_neg_test_cases", "get_neg_feedback")
    workflow.add_edge("generate_edge_test_cases", "get_edge_feedback")
    workflow.add_edge("get_pos_feedback", "correct_pos_tests")
    workflow.add_edge("get_neg_feedback", "correct_neg_tests")
    workflow.add_edge("get_edge_feedback", "correct_edge_tests")
    workflow.add_edge('correct_pos_tests', 'combine_csv_files')
    workflow.add_edge('correct_neg_tests', 'combine_csv_files')
    workflow.add_edge('correct_edge_tests', 'combine_csv_files')
    workflow.add_edge('combine_csv_files', END)

    app = workflow.compile()

    return app

# -------------------------------------Graph for Update System------------------------------------ #
class UpdateGraphState(TypedDict):
    """
    Represents the state of the graph workflow for updating test cases based on requirement changes.

    Attributes:
        old_csv_file_path (str): Path to the old requirements CSV file.
        new_csv_file_path (str): Path to the updated requirements CSV file.
        old_test_cases_file_path (str): Path to the old test cases file.
        output_file_path (str): Path for the updated output file.
        type_gen (str): Type of test case generation (positive, negative, edge).
        rem_filtered_path (str): Path for storing removed test cases.
        new_test_path (str): Path for storing newly generated test cases.
        update_test_path (str): Path for storing updated test cases.
        removed_sections (List[Dict]): List of removed sections in the requirements.
        new_sections (List[Dict]): List of new sections in the requirements.
        changed_rows (List[Dict]): List of changed rows in the requirements.
    """
    old_csv_file_path: str
    new_csv_file_path: str
    old_test_cases_file_path: str
    output_file_path: str
    type_gen: str
    rem_filtered_path: str
    new_test_path: str
    update_test_path: str
    removed_sections: List[Dict]
    new_sections: List[Dict]
    changed_rows: List[Dict]

def create_update_graph_workflow():
    """
    Creates the graph workflow for updating test cases based on requirement changes.

    The workflow includes the following steps:
        1. Identify differences between the old and new requirements.
        2. Remove outdated test cases based on removed requirements.
        3. Generate new test cases for added requirements.
        4. Update existing test cases for modified requirements.
        5. Finalize the output by combining all updated test cases.

    Returns:
        Callable: The compiled graph workflow application.
    """
    update_workflow = StateGraph(UpdateGraphState)

    # Define the nodes
    update_workflow.add_node("check_differences", check_differences)
    update_workflow.add_node("remove_test_cases", remove_test_cases)
    update_workflow.add_node("new_test_cases", new_test_cases)
    update_workflow.add_node("update_test_cases", update_test_cases)
    update_workflow.add_node("finalize_csv", finalize_csv)

    # Build the graph
    update_workflow.add_edge(START, 'check_differences')
    update_workflow.add_edge('check_differences', 'remove_test_cases')
    update_workflow.add_edge('check_differences', 'new_test_cases')
    update_workflow.add_edge('check_differences', 'update_test_cases')
    update_workflow.add_edge('remove_test_cases', 'finalize_csv')
    update_workflow.add_edge('new_test_cases', 'finalize_csv')
    update_workflow.add_edge('update_test_cases', 'finalize_csv')

    update_app = update_workflow.compile()
    return update_app