# Imports
import json 
from langchain_core.prompts import Chat<PERSON>romptTemplate, PromptTemplate
from pydantic import BaseModel, Field
from typing import List, Dict
from langchain_openai import AzureChatOpenAI
from langchain_core.output_parsers import StrOutputParser
from typing_extensions import TypedDict
from IPython.display import Image
from pprint import pprint
import os
from langchain_community.document_loaders.csv_loader import CSVLoader
from dotenv import load_dotenv
import pandas as pd
import csv
import re
import io


# Functions
def get_csv_contents(file_path):
    csv_file_contents = ""
    with open(file_path, "r", encoding="utf-8", errors="replace") as file:
        reader = csv.reader(file)
        next(reader)  # Skip the header row
        for row in reader:
            csv_file_contents += f"Req ID: {row[0]}\n"
            csv_file_contents += f"Name: {row[1]}\n"
            desc = row[2].replace('\\n', '\n').replace("\n", ". ").replace("\r", "")
            csv_file_contents += f"Description: {desc}"
            csv_file_contents += "\n\n"

    return csv_file_contents

def generate_test_case_csv(test_cases, prefix, output_file='test_cases.csv', req_desc_file='requirements.csv'):
    # Verify that prefix is one of the allowed values
    if prefix not in ['p', 'n', 'e']:
        raise ValueError("Prefix must be 'p', 'n', or 'e'.")

    # Load requirement descriptions from the additional CSV file using pandas
    req_descriptions_df = pd.read_csv(req_desc_file)
    # Rename columns for consistency, if necessary
    req_descriptions_df.columns = ['requirement_id', 'Name', 'requirement_description']

    # Convert test cases to a pandas DataFrame
    test_case_data = []
    for idx, test_case in enumerate(test_cases):
        test_case_id = f"{prefix}{test_case.requirement_id}_{idx + 1}"
        test_case_data.append({
            'test_case_id': test_case_id,
            'requirement_id': int(test_case.requirement_id),
            'test_case_description': test_case.test_case_description,
            'inputs': test_case.inputs,
            'expected_outcome': test_case.expected_outcome
        })

    test_cases_df = pd.DataFrame(test_case_data)

    # Merge the test cases DataFrame with the requirement descriptions DataFrame on requirement_id
    merged_df = pd.merge(test_cases_df, req_descriptions_df[['requirement_id', 'requirement_description']], 
                         on='requirement_id', how='left')

    # Fill in any missing descriptions with a default message (avoid inplace=True to avoid the warning)
    merged_df['requirement_description'] = merged_df['requirement_description'].fillna("No description available")

    # Save the merged DataFrame to CSV
    merged_df.to_csv(output_file, index=False)
    print(f"CSV file '{output_file}' has been created successfully.")

def csv_to_string(file_path):
    # Load the CSV file into a DataFrame
    df = pd.read_csv(file_path)

    # Remove \n and \r characters from the requirement_description column
    df['requirement_description'] = df['requirement_description'].str.replace(r'[\n\r]', '', regex=True)

    # Initialize an empty list to store each row as a comma-separated string
    rows = []
    rows.append("test_case_id,requirement_id,test_case_description,inputs,expected_outcome,requirement_description")
    # Iterate through each row and join values with commas, without spaces
    for _, row in df.iterrows():
        row_string = ','.join(str(value) for value in row)
        rows.append(row_string)

    # Join all rows with a newline character
    final_string = '\n'.join(rows)

    return final_string

def get_feedback_str(feedback_lst):
    s = "test_case_id,feedback\n"
    for feedback in feedback_lst:
        s += feedback.test_case_id + "," + feedback.feedback + "\n"
    return s

def get_filtered_generated_test_cases(res_file, feedback_res):
    test_case_ids = []
    for feedback in feedback_res.feedback_list:
        test_case_ids.append(feedback.test_case_id)
    df = pd.read_csv(res_file)
    # Remove \n and \r characters from the requirement_description column
    df['requirement_description'] = df['requirement_description'].str.replace(r'[\n\r]', '', regex=True)

    # Initialize an empty list to store each row as a comma-separated string
    rows = []
    rows.append("test_case_id,requirement_id,test_case_description,inputs,expected_outcome,requirement_description")
    
    for _, row in df.iterrows():
        if row['test_case_id'] in test_case_ids:
            rows.append(f"{row['test_case_id']},{row['requirement_id']},{row['test_case_description']},{row['inputs']},{row['expected_outcome']},{row['requirement_description']}")
    
    # Join all rows into a single CSV-formatted string
    csv_output = "\n".join(rows)
    
    return csv_output

def replace_test_cases(corrected_cases, csv_file):
    # Load the CSV file into a DataFrame
    df = pd.read_csv(csv_file)
    
    # Convert corrected cases into a DataFrame
    corrected_df = pd.DataFrame([case.__dict__ for case in corrected_cases])
    
    # Iterate through each row in corrected_df to update matching rows in df
    for _, row in corrected_df.iterrows():
        test_case_id = row['test_case_id']
        
        # Check if the test_case_id exists in the CSV DataFrame
        if test_case_id in df['test_case_id'].values:
            # Locate the index of the matching row in the original DataFrame
            idx = df[df['test_case_id'] == test_case_id].index[0]
            
            # Update only the columns present in corrected_df for the matched row
            for col in corrected_df.columns:
                df.at[idx, col] = row[col]
    
    # Save the updated DataFrame back to the CSV file
    df.to_csv(csv_file, index=False)


# ---------------------Utils for Update system------------------------------------------------ #
def read_csv(file_path: str, unique_column: str, encoding: str = 'utf-8') -> Dict[str, Dict]:
    """
    Reads a CSV file and converts it into a dictionary with the unique column as the key.
    """
    with open(file_path, 'r', encoding=encoding, errors='replace') as file:
        reader = csv.DictReader(file)
        data = {row[unique_column]: row for row in reader}
    return data

def get_diff(file1_path: str, file2_path: str, unique_column: str):
    """
    Finds new, removed, and modified rows based on the unique column in the CSV files.
    """
    data1 = read_csv(file1_path, unique_column)
    data2 = read_csv(file2_path, unique_column)

    new_rows = {key: data2[key] for key in data2 if key not in data1}
    removed_rows = {key: data1[key] for key in data1 if key not in data2}
    modified_rows = {
        key: {'old_row': data1[key], 'new_row': data2[key]}
        for key in data1
        if key in data2 and data1[key] != data2[key]
    }

    return new_rows, removed_rows, modified_rows

def get_new_sections(file1_path: str, file2_path: str, unique_column: str) -> List[Dict]:
    """
    Returns rows that are newly added in file2.
    """
    new_rows, _, _ = get_diff(file1_path, file2_path, unique_column)
    return list(new_rows.values())

def get_removed_sections(file1_path: str, file2_path: str, unique_column: str) -> List[Dict]:
    """
    Returns rows that are removed in file2.
    """
    _, removed_rows, _ = get_diff(file1_path, file2_path, unique_column)
    return list(removed_rows.values())

def get_changed_rows(file1_path: str, file2_path: str, unique_column: str) -> List[Dict]:
    """
    Returns rows that are modified between file1 and file2.
    """
    _, _, modified_rows = get_diff(file1_path, file2_path, unique_column)
    return [
        {'Req ID': key, 'old_row': value['old_row'], 'new_row': value['new_row']}
        for key, value in modified_rows.items()
    ]

def remove_rows_by_req_id(input_file: str, req_ids_to_remove: list, unique_column: str = "Req ID"):
    """
    Removes rows with specified Req IDs from a CSV file using pandas and writes the result to a new file.

    Args:
        input_file (str): Path to the input CSV file.
        output_file (str): Path to the output CSV file.
        req_ids_to_remove (list): List of Req IDs to remove.
        unique_column (str): Name of the column used for identifying rows. Defaults to "Req ID".
    """
    # Load the CSV into a DataFrame
    df = pd.read_csv(input_file)
    
    # Filter out rows with Req IDs in the list
    filtered_df = df[~df[unique_column].isin(req_ids_to_remove)]
    
    # Write the filtered DataFrame to the output file
    filtered_df.to_csv(input_file, index=False)

def df_to_custom_string(df):
    result = ""
    # Iterate through rows and append each column's value
    for index, row in df.iterrows():
        for col in df.columns:
            # Ensure the value is a string and remove newlines/carriage returns
            value = str(row[col]).replace("\n", "").replace("\r", "")
            result += f"  {col}: {value}"
        result += "\n"
    return result

def changed_rows_to_str(changed_rows): 
    s = ""
    for row in changed_rows:
        s += "Old Requirement: " + "Req ID: " + row['old_row']['Req ID'] + ", Name: " + row['old_row']['Name'] + ", Description: " + row['old_row']['Description'].replace("\n", "").replace("\r", "")
        s += '\n'
        s += "New Requirement: " + "Req ID: " + row['new_row']['Req ID'] + ", Name: " + row['new_row']['Name'] + ", Description: " + row['new_row']['Description'].replace("\n", "").replace("\r", "")
    return s

def generate_updated_test_case_csv(pos_updated, neg_updated, edge_updated, output_file='updated_cases.csv', req_desc_file='../test2.csv'):

    # Load requirement descriptions from the additional CSV file using pandas
    req_descriptions_df = pd.read_csv(req_desc_file)
    # Rename columns for consistency, if necessary
    req_descriptions_df.columns = ['requirement_id', 'Name', 'requirement_description']

    # Convert test cases to a pandas DataFrame
    test_case_data = []
    for idx, test_case in enumerate(pos_updated):
        test_case_id = f"p{test_case.requirement_id}_{idx + 1}"
        test_case_data.append({
            'test_case_id': test_case_id,
            'requirement_id': int(test_case.requirement_id),
            'test_case_description': test_case.test_case_description,
            'inputs': test_case.inputs,
            'expected_outcome': test_case.expected_outcome
        })
    for idx, test_case in enumerate(neg_updated):
        test_case_id = f"n{test_case.requirement_id}_{idx + 1}"
        test_case_data.append({
            'test_case_id': test_case_id,
            'requirement_id': int(test_case.requirement_id),
            'test_case_description': test_case.test_case_description,
            'inputs': test_case.inputs,
            'expected_outcome': test_case.expected_outcome
        })
    for idx, test_case in enumerate(edge_updated):
        test_case_id = f"e{test_case.requirement_id}_{idx + 1}"
        test_case_data.append({
            'test_case_id': test_case_id,
            'requirement_id': int(test_case.requirement_id),
            'test_case_description': test_case.test_case_description,
            'inputs': test_case.inputs,
            'expected_outcome': test_case.expected_outcome
        })

    test_cases_df = pd.DataFrame(test_case_data)

    # Merge the test cases DataFrame with the requirement descriptions DataFrame on requirement_id
    merged_df = pd.merge(test_cases_df, req_descriptions_df[['requirement_id', 'requirement_description']], 
                         on='requirement_id', how='left')

    # Fill in any missing descriptions with a default message (avoid inplace=True to avoid the warning)
    merged_df['requirement_description'] = merged_df['requirement_description'].fillna("No description available")

    # Save the merged DataFrame to CSV
    merged_df.to_csv(output_file, index=False)
    print(f"CSV file '{output_file}' has been created successfully.")


def generate_gen_updated_test_case_csv(updated_cases, output_file='updated_gen_cases.csv', req_desc_file='test2.csv'):
    # Load requirement descriptions from the additional CSV file using pandas
    req_descriptions_df = pd.read_csv(req_desc_file)
    # Rename columns for consistency, if necessary
    req_descriptions_df.columns = ['requirement_id', 'Name', 'requirement_description']

    # Convert test cases to a pandas DataFrame
    test_case_data = []
    for idx, test_case in enumerate(updated_cases):
        if test_case.test_type == 'positive':
            prefix = 'p'
        elif test_case.test_type == 'negative':
            prefix = 'n'
        else:
            prefix = 'e'
        test_case_id = f"{prefix}{test_case.requirement_id}_{idx + 1}"
        test_case_data.append({
            'test_case_id': test_case_id,
            'requirement_id': int(test_case.requirement_id),
            'test_case_description': test_case.test_case_description,
            'inputs': test_case.inputs,
            'expected_outcome': test_case.expected_outcome
        })
    test_cases_df = pd.DataFrame(test_case_data)

    # Merge the test cases DataFrame with the requirement descriptions DataFrame on requirement_id
    merged_df = pd.merge(test_cases_df, req_descriptions_df[['requirement_id', 'requirement_description']], 
                         on='requirement_id', how='left')

    # Fill in any missing descriptions with a default message (avoid inplace=True to avoid the warning)
    merged_df['requirement_description'] = merged_df['requirement_description'].fillna("No description available")

    # Save the merged DataFrame to CSV
    merged_df.to_csv(output_file, index=False)
    print(f"CSV file '{output_file}' has been created successfully.")
