# Abbott Software Life Cycle (SLC) Virtual Assistant

Software is a critical component of our products and digital assets. Working in a regulated industry, we’re required 
to meet strict policies, standards, and follow internal procedures. Consequently, companies in this industry use a 
rigorous software lifecycle process (SLC). This increases delivery demands on trained project personnel and 
quality analysts, and reduces opportunities for business managers to adjust to changing consumer expectations. 

Project: Software Lifecycle (SLC) AI Assistant
In this project we'll explore the potential of an AI virtual assistant to aid project personnel to find answers to 
procedure questions, assess complex information, and generate content. Some specific examples include: 

• Drafting requirements
• Creating test plans and analyzing test coverage
• Generating design documents 

We'll assess the technical feasibility with a single task and assess the overall business viability of an SLC AI 
Assistant. What should such an assistant focus on? What is the business case? How might you construct such 
an assistant? Develop a proof of concept and business case to demonstrate your approach.

# Technical Skills: 
• ML, NLP, Generative AI (LLM, prompt architecture)
