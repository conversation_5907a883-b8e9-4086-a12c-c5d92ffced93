# Imports
import tiktoken
import os
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.load import dumps, loads


# Function that prints some token statistics of the text data
def print_num_tokens(model_name, text_dict):
    """
    Prints token statistics for a dictionary of text data.

    Args:
        model_name (str): The name of the model to use for tokenization.
        text_dict (dict): A dictionary where keys are file names and values are text content.

    Returns:
        None
    """
    token_num_dict = {}
    encoding = tiktoken.encoding_for_model(model_name)
    for file_name, text in text_dict.items():
        text_tokens = encoding.encode(text)
        print(f"File name: {file_name}, No. of Tokens: {len(text_tokens)}")
        token_num_dict[str(file_name)] = len(text_tokens)
    # Average number of token
    num_token_lst = list(token_num_dict.values())
    avg_tokens = sum(num_token_lst) / len(num_token_lst)
    print(f"Average number of tokens in file: {avg_tokens}")
    print(f"Max number of tokens in a file: {max(num_token_lst)}")
    print(f"Min number of token in a file: {min(num_token_lst)}")
    
    # Find file names with min and max tokens
    min_tokens_file = min(token_num_dict, key=token_num_dict.get)
    max_tokens_file = max(token_num_dict, key=token_num_dict.get)
    print(f"File with minimum tokens: {min_tokens_file}")
    print(f"File with maximum tokens: {max_tokens_file}")

# Removing the headers and the details about
def remove_unused_info(text: str) -> str:
    """
    Removes unused headers, footers, and confidential information from the text.

    Args:
        text (str): The input text to process.

    Returns:
        str: The cleaned text with unused information removed.
    """
    marker = "END OF DOCUMENT"
    # Find the position of the end of document
    pos = text.find(marker)
    if pos != -1:
        text = text[:pos + len(marker)]
    else:
        print("END OF DOCUMENT NOT FOUND")
 
    start_marker = "Abbott – Business and Technology Services"
    end_marker = "This information is confidential to Abbott. The user is responsible for using the appropriate version of this document."
 
    start_marker_pos = text.find(start_marker)
    end_marker_pos = text.find(end_marker)
    while start_marker_pos != -1 and end_marker_pos > start_marker_pos and "Document Type" in text[start_marker_pos:end_marker_pos]:
        text = text[:start_marker_pos] + ' ' + text[end_marker_pos + len(end_marker):] 
        start_marker_pos = text.find(start_marker)
        end_marker_pos = text.find(end_marker)
    return text

# Load the text files as text_dict
# Specify the folder where the text files are stored
def load_text_files(input_folder):
    """
    Loads text files from a specified folder into a dictionary.

    Args:
        input_folder (str): The path to the folder containing the text files.

    Returns:
        dict: A dictionary where keys are file names (without extensions) and values are file contents.
    """
    input_folder = 'text_files'

    # Initialize an empty dictionary to store the file contents
    text_dict = {}

    # Iterate through each file in the folder
    for file_name in os.listdir(input_folder):
        if file_name.endswith('.txt'):
            file_path = os.path.join(input_folder, file_name)
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                # Use the file name (without extension) as the key
                key = os.path.splitext(file_name)[0]
                text_dict[key] = content

    print("All files have been loaded into text_dict.")
    return text_dict

def create_chunks(text_dict, chunk_size=2000, chunk_overlap=200):
    """
    Splits text data into smaller chunks using a RecursiveCharacterTextSplitter.

    Args:
        text_dict (dict): A dictionary of text data where keys are filenames and values are text content.
        chunk_size (int, optional): The size of each chunk. Defaults to 2000.
        chunk_overlap (int, optional): The overlap size between chunks. Defaults to 200.

    Returns:
        list: A list of Document objects containing the text chunks with metadata.
    """
    text_splitter = RecursiveCharacterTextSplitter(chunk_size = chunk_size, 
                                                   chunk_overlap = chunk_overlap)
    chunks = []
    for filename, text in text_dict.items():
        file_chunks = text_splitter.create_documents([text], metadatas=[{"source": filename}])
        for file_chunk in file_chunks: 
            chunks.append(file_chunk)
    return chunks

def format_docs(docs):
    """
    Formats a list of Document objects into a single string with documents separated by double newlines.

    Args:
        docs (list): A list of Document objects.

    Returns:
        str: The formatted string of concatenated document contents.
    """
    return "\n\n".join(doc.page_content for doc in docs)


## Function for RRF (Reciprocal RAG fusion)
def rrf(results, k=60, top_k = 3):
    """
    Implements Reciprocal Rank Fusion (RRF) for ranking search results.

    Args:
        results (list): A list of ranked lists of documents (results from different methods).
        k (int, optional): The ranking base for score calculation. Defaults to 60.
        top_k (int, optional): The number of top results to return. Defaults to 3.

    Returns:
        list: A list of top-ranked documents and their fused scores.
    """
    fused_scoes = {}

    for docs in results:
        for rank, doc in enumerate(docs):
            doc_str = dumps(docs)
            if doc_str not in fused_scoes:
                fused_scoes[doc_str] = 0

            prev_score = fused_scoes[doc_str]
            # Update the score of the document
            fused_scoes[doc_str] += 1 / (rank + k)

    # Sort the documents based on the fused scores
    reranked_results = [
        (loads(doc), score)
        for doc, score in sorted(fused_scoes.items(), key=lambda x: x[1], reverse=True)
    ]

    # Return the top k reranked results
    return reranked_results[:top_k]





