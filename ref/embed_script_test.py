# Imports
from dotenv import load_dotenv
import time
import os
from langchain_chroma import Chroma
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings
from pathlib import Path
import os
from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings
from pathlib import Path
import chromadb
from os import listdir
from os.path import isfile, join
import gc

# Load the .env file
load_dotenv()
os.environ['AZURE_OPENAI_ENDPOINT'] = os.getenv("AZURE_OPENAI_EMBEDDING_ENDPOINT")
os.environ['OPENAI_API_VERSION'] = os.getenv("AZURE_EMBEDDING_API_VERSION")
embeddings = AzureOpenAIEmbeddings(model='text-embedding-3-small')

class Document:
    def __init__(self, id, page_content, metadata):
        self.id = id
        self.page_content = page_content
        self.metadata = metadata


# Set up exponential backoff parameters
def retry_with_backoff(api_call, *args, max_retries=5, initial_wait=8, **kwargs):
    retries = 0
    wait_time = initial_wait
    while retries < max_retries:
        try:
            return api_call(*args, **kwargs)
        except Exception as e:
            print(f"Rate limit exceeded. Retrying in {wait_time} seconds...")
            time.sleep(wait_time)
            retries += 1
            wait_time *= 8  # Exponential backoff
            
    raise Exception("Max retries exceeded")

# Initialize the vector store
vector_store = Chroma(
    collection_name='bts-slc-db',
    embedding_function=embeddings,
    persist_directory='./chroma_db_new1'
)

def embed_and_store_text_chunks(file_paths, excluded_files):
    embedding_processed_files = "embedding_processed_files.txt"

    if not os.path.exists(embedding_processed_files):
        with open(embedding_processed_files, 'w') as file:
            pass

    with open(embedding_processed_files, "r") as file:
        embedding_processed_file_paths = file.read()

    for file_path in file_paths:
        if file_path in embedding_processed_file_paths or file_path in excluded_files:
            continue
        print(file_path)
        with open(file_path, "r", encoding='utf-8') as f:
            print(f"Opening File: {file_path}")
            text = f.read()

        # Split text into chunks
        split_text = text.split('page_content')
 
        # Remove any leading or trailing whitespace from each split part
        split_text = [part.strip()[2:-1] for part in split_text[1:]]

        cleaned_path = file_path.replace("./contextualized_chunks_files/", "")

        # Convert to Document formatted chunks
        chunk_docs = [Document(id=f"{file_path}_{i}", page_content=chunk, metadata={"source": cleaned_path}) for i, chunk in enumerate(split_text)]

        # Embed each chunk with exponential backoff
        _embeddings = []
        for chunk in split_text:
            embedding_result = retry_with_backoff(lambda: embeddings.embed_documents([chunk]))
            _embeddings.extend(embedding_result)
    
        try:
            vector_store.add_documents(documents=chunk_docs, embeddings=_embeddings)
            print("Added to vector store!")
        except Exception as e:
            print(f"Error adding documents to vector store: {e}")

        with open(embedding_processed_files, 'a', encoding='utf-8') as file:
            file.write(" " + file_path)

        print("Processed: " + file_path)

        del text, split_text, chunk_docs, _embeddings
        gc.collect()

file_paths = []
for f in listdir("./contextualized_chunks_files"):
   if isfile((join("./contextualized_chunks_files/", f))):
        file_paths.append("./contextualized_chunks_files/" + f)

if __name__ == '__main__': 
    excluded_files = []
    embed_and_store_text_chunks(file_paths, excluded_files)