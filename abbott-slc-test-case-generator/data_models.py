# Imports
from pydantic import BaseModel, Field
from typing import List

# Pydantic for structured output
class TestCase(BaseModel):
    """
    Represents a single test case for a requirement.

    Attributes:
        requirement_id (str): The ID of the requirement being tested.
        test_case_description (str): Description of the test case that validates the requirement.
        inputs (str): A list or description of inputs required for the test case.
        expected_outcome (str): The expected result or outcome of the test case.
    """
    requirement_id: str = Field(description="Requirement Id from the csv")
    # requirement_description: str = Field(description="Requirement text from the csv")
    test_case_description: str = Field(description="Test case that validates the requirement")
    inputs: str = Field(description="List of inputs for the test case")
    expected_outcome: str = Field(description="Expected result of the test case")

class TestCases(BaseModel):
    """
    Represents a collection of test cases.

    Attributes:
        test_cases (List[TestCase]): A list of individual test case objects.
    """
    test_cases: List[TestCase] = Field(description="A list of the test cases")

### Feedback Chain
class FeedbackTemplate(BaseModel):
    """
    Represents feedback for a single test case.

    Attributes:
        test_case_id (str): The ID of the test case that has issues.
        feedback (str): Detailed feedback identifying the issues in the test case, 
                        including what is incorrect, missing, or needs modification.
    """
    test_case_id: str = Field(description="The ID of the test case which is incorrect, incomplete, or contains inaccuracies or irrelevant details.")
    feedback: str = Field(description="Detailed feedback identifying the issues in the test case, including what is incorrect, missing, or needs modification.")

class Feedback(BaseModel):
    """
    Represents a collection of feedback entries for test cases.

    Attributes:
        feedback_list (List[FeedbackTemplate]): A list of feedback entries, each identifying issues 
                                                in specific test cases and providing corrections or improvements.
    """
    feedback_list: List[FeedbackTemplate] = Field(description="A list of feedback entries, each identifying issues in specific test cases that are incorrect, incomplete, or contain inaccuracies. Each entry includes the requirement ID and corresponding feedback details.")

# Pydantic for structured output
class CorrectedTestCase(BaseModel):
    """
    Represents a corrected test case based on feedback.

    Attributes:
        test_case_id (str): The ID of the test case being corrected.
        test_case_description (str): The updated description of the test case addressing the feedback.
        inputs (str): The updated inputs for the test case.
        expected_outcome (str): The updated expected result of the test case.
    """
    test_case_id: str = Field(description="test case Id from the feedback")
    test_case_description: str = Field(description="Test case that validates the requirement and the feedback")
    inputs: str = Field(description="List of inputs for the test case")
    expected_outcome: str = Field(description="Expected result of the test case")

class CorrectedTestCases(BaseModel):
    """
    Represents a collection of corrected test cases.

    Attributes:
        test_cases (List[CorrectedTestCase]): A list of corrected test cases.
    """
    test_cases: List[CorrectedTestCase] = Field(description="A list of the test cases")

class GenTestCase(BaseModel):
    """
    Represents a generated test case with a test type.

    Attributes:
        requirement_id (str): The ID of the requirement being tested.
        test_case_description (str): Description of the test case that validates the requirement.
        inputs (str): A list or description of inputs required for the test case.
        expected_outcome (str): The expected result or outcome of the test case.
        test_type (str): The type of the test case, such as positive, negative, or edge.
    """
    requirement_id: str = Field(description="Requirement Id from the csv")
    test_case_description: str = Field(description="Test case that validates the requirement")
    inputs: str = Field(description="List of inputs for the test case")
    expected_outcome: str = Field(description="Expected result of the test case")
    test_type: str = Field(description="Type of the test case: positive, negative, or edge")  # New field

class GenTestCases(BaseModel):
    """
    Represents a collection of generated test cases.

    Attributes:
        test_cases (List[GenTestCase]): A list of individual test case objects, each with a specified test type.
    """
    test_cases: List[GenTestCase] = Field(description="A list of the test cases")
