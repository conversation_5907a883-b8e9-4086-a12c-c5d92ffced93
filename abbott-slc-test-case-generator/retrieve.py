# Imports
from utils import * 
from bm25retrieve import BM25<PERSON><PERSON>riever
from langchain_openai import AzureOpenAIEmbeddings
from langchain_chroma import Chroma
from os import listdir
from os.path import isfile, join
from langchain_core.documents import Document

class RetrieveVectorDbWithBM25:
    """
    A retriever that combines vector database search and BM25-based keyword search
    for better relevance in document retrieval.

    Attributes:
        vector_db (Chroma): The vector database for similarity-based retrieval.
        bm25_retriever (BM25Retriever): The BM25 retriever for keyword-based search.
    """

    def __init__(self, vector_db, bm25_retriever):
        """
        Initializes the retriever with a vector database and a BM25 retriever.

        Args:
            vector_db (Chroma): The vector database for similarity search.
            bm25_retriever (BM25Retriever): The BM25-based retriever.
        """
        self.vector_db = vector_db
        self.bm25_retriever = bm25_retriever

    def vector_db_search(self, query, k=3):
        """
        Performs a similarity search on the vector database.

        Args:
            query (str): The search query.
            k (int): The number of top results to retrieve.

        Returns:
            tuple: Two dictionaries, one with document content as keys and scores as values,
                   and another with document sources as keys and scores as values.
        """
        search_res = dict()
        docs_and_scores = self.vector_db.similarity_search_with_relevance_scores(query=query, k=k)
        for doc, score in docs_and_scores:
            search_res[doc.page_content] = score
        sorted_res = {doc: score for doc, score in sorted(search_res.items(), key=lambda x: x[1], reverse=True)}
        # Code for source
        source_res = dict()
        for doc, score in docs_and_scores:
            source_res[doc.metadata['source']] = score
        source_sorted_res = {doc: score for doc, score in sorted(source_res.items(), key=lambda x: x[1], reverse=True)}
        return sorted_res, source_sorted_res
    
    def bm25_search(self, query, k):
        """
        Performs a keyword-based search using BM25.

        Args:
            query (str): The search query.
            k (int): The number of top results to retrieve.

        Returns:
            tuple: Two dictionaries, one with document content as keys and scores as values,
                   and another with document sources as keys and scores as values.
        """
        relevant_docs, scores = self.bm25_retriever.get_relevant_documents(query, k=3)
        doc_score_dict = {doc.page_content: score for doc, score in zip(relevant_docs, scores)}
        source_score_dict = {doc.metadata['source']: score for doc, score in zip(relevant_docs, scores)}
        return doc_score_dict, source_score_dict
    
    def normalize_dict(self, input_dict):
        """
        Normalizes the values in a dictionary to a range between 0.05 and 1.

        Args:
            input_dict (dict): The dictionary with values to normalize.

        Returns:
            dict: A dictionary with normalized values.
        """
        epsilon = 0.05
        min_value = min(input_dict.values())
        max_value = max(input_dict.values())
        a, b = 0.05, 1
        if max_value == min_value: 
            return {k: b if max_value > 0.5 else a for k in input_dict.keys()}
        
        return {k: a + ((v - min_value) / (max_value - min_value)) * (b - a) for k, v in input_dict.items()}
    
    def combine_results(self, vector_db_res, vector_source, bm25_res, bm_source):
        """
        Combines and normalizes results from vector database and BM25 searches.

        Args:
            vector_db_res (dict): Results from the vector database search.
            vector_source (dict): Source scores from the vector database search.
            bm25_res (dict): Results from the BM25 search.
            bm_source (dict): Source scores from the BM25 search.

        Returns:
            tuple: Two dictionaries combining the results and source scores from both methods.
        """
        norm_vector_db_res = self.normalize_dict(vector_db_res)
        norm_bm25_res = self.normalize_dict(bm25_res)
        norm_vector_source = self.normalize_dict(vector_source)
        norm_bm_source = self.normalize_dict(bm_source)
        
        # Combine the dicts
        combined_dict = {}
        for k, v in norm_vector_db_res.items():
            combined_dict[k] = v

        for k, v in norm_bm25_res.items():
            if k in combined_dict: 
                combined_dict[k] = max(combined_dict[k], v)
            else:
                combined_dict[k] = v

        # For the source
        source_dict = {}
        for k, v in norm_vector_source.items():
            source_dict[k] = v

        for k, v in norm_bm_source.items():
            if k in source_dict: 
                source_dict[k] = max(source_dict[k], v)
            else:
                source_dict[k] = v
        return combined_dict, source_dict
    
    def search(self, query, k, bm_search=True):
        """
        Performs a combined search using both vector database and BM25 retrievers.

        Args:
            query (str): The search query.
            k (int): The number of top results to retrieve.
            bm_search (bool): Whether to include BM25 search in the results.

        Returns:
            tuple: Sorted results and source scores from the combined search.
        """
        vector_db_res, vector_source = self.vector_db_search(query, k)

        if bm_search:
            bm_res, bm_source = self.bm25_search(query, k)
            if bm_res:
                combined_res, source_dict = self.combine_results(vector_db_res, vector_source, 
                                                                 bm_res, bm_source)
                # Sort the scores
                sorted_dict = sorted(combined_res.items(), key=lambda x: x[1], reverse=True)
                source_sorted_dict = sorted(source_dict.items(), key=lambda x: x[1], reverse=True)
                return sorted_dict, source_sorted_dict
        return vector_db_res, vector_source


def get_chunks_bm25():
    """
    Retrieves pre-processed document chunks from files for BM25 retrieval.

    Returns:
        list: A list of Document objects representing chunks of text with metadata.
    """
    file_paths = []
    for f in listdir("contextualized_chunks_files"):
        if isfile((join("./contextualized_chunks_files", f))):
                file_paths.append("./contextualized_chunks_files/" + f)

    chunks = []

    for file_path in file_paths:
        with open(file_path, "r", encoding='utf-8') as f:
            text = f.read()

        split_text = text.split('page_content')

        # Remove any leading or trailing whitespace from each split part
        split_text = [part.strip()[2:-1] for part in split_text[1:]]

        cleaned_path = file_path.replace("./contextualized_chunks_files/", "")
        
        # Convert to Document formatted chunks
        for txt in split_text:
            document = Document(
                page_content=txt,
                metadata = {"source": cleaned_path}
            )
            chunks.append(document)

    return chunks


def create_retriever(chroma_path):
    """
    Creates a retriever combining vector database and BM25 search.

    Args:
        chroma_path (str): Path to the Chroma database directory.

    Returns:
        RetrieveVectorDbWithBM25: A combined retriever instance.
    """
    # Get the keys from the .env file
    azure_endpoint = os.getenv("AZURE_OPENAI_EMBEDDING_ENDPOINT")
    api_version = os.getenv("AZURE_EMBEDDING_API_VERSION")
    # Create the embedding model
    embeddings = AzureOpenAIEmbeddings(model='text-embedding-3-small',
                                       azure_endpoint=azure_endpoint,
                                       api_version=api_version)

    # Load the predefined vector store
    vector_store = Chroma(
        embedding_function=embeddings,
        persist_directory=chroma_path,
        collection_name='bts-slc-db'
    )

    # Get the chunks for BM25 Retriever
    chunks = get_chunks_bm25()

    # Create the BM25 Retriever
    bm25_retriever = BM25Retriever(chunks)

    # Create the combined retriever
    retriever = RetrieveVectorDbWithBM25(vector_db=vector_store, bm25_retriever=bm25_retriever)
    return retriever


