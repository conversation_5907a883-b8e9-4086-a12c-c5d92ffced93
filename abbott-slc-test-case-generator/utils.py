# Imports
from typing import List, Dict
import pandas as pd
import csv


# Functions
def get_csv_contents(file_path):
    """
    Reads a CSV file and converts its contents into a formatted string for processing.

    Args:
        file_path (str): Path to the input CSV file.

    Returns:
        str: A string representation of the CSV file contents.
    """
    csv_file_contents = ""
    with open(file_path, "r", encoding="utf-8", errors="replace") as file:
        reader = csv.reader(file)
        next(reader)  # Skip the header row
        for row in reader:
            csv_file_contents += f"Req ID: {row[0]}\n"
            csv_file_contents += f"Name: {row[1]}\n"
            desc = row[2].replace('\\n', '\n').replace("\n", ". ").replace("\r", "")
            csv_file_contents += f"Description: {desc}"
            csv_file_contents += "\n\n"

    return csv_file_contents

def generate_test_case_csv(test_cases, prefix, output_file, req_desc_file):
    """
    Generates a test case CSV file by merging test case data with requirement descriptions.

    Args:
        test_cases (list): List of test case objects.
        prefix (str): Prefix for test case IDs ('p', 'n', or 'e').
        output_file (str): Path to save the generated CSV file.
        req_desc_file (str): Path to the requirements description CSV file.

    Raises:
        ValueError: If the prefix is not one of 'p', 'n', or 'e'.

    Returns:
        None
    """
    # Verify that prefix is one of the allowed values
    if prefix not in ['p', 'n', 'e']:
        raise ValueError("Prefix must be 'p', 'n', or 'e'.")

    # Load requirement descriptions from the additional CSV file using pandas
    req_descriptions_df = pd.read_csv(req_desc_file)
    # Rename columns for consistency, if necessary
    req_descriptions_df.columns = ['requirement_id', 'Name', 'requirement_description']

    # Convert test cases to a pandas DataFrame
    test_case_data = []
    for idx, test_case in enumerate(test_cases):
        test_case_id = f"{prefix}{test_case.requirement_id}_{idx + 1}"
        test_case_data.append({
            'test_case_id': test_case_id,
            'requirement_id': int(test_case.requirement_id),
            'test_case_description': test_case.test_case_description,
            'inputs': test_case.inputs,
            'expected_outcome': test_case.expected_outcome
        })

    test_cases_df = pd.DataFrame(test_case_data)

    # Merge the test cases DataFrame with the requirement descriptions DataFrame on requirement_id
    merged_df = pd.merge(test_cases_df, req_descriptions_df[['requirement_id', 'requirement_description']], 
                         on='requirement_id', how='left')

    # Fill in any missing descriptions with a default message (avoid inplace=True to avoid the warning)
    merged_df['requirement_description'] = merged_df['requirement_description'].fillna("No description available")

    # Save the merged DataFrame to CSV
    merged_df.to_csv(output_file, index=False)
    print(f"CSV file '{output_file}' has been created successfully.")

def csv_to_string(file_path):
    """
    Converts a CSV file into a formatted string representation.

    Args:
        file_path (str): Path to the input CSV file.

    Returns:
        str: A string representation of the CSV contents.
    """
    # Load the CSV file into a DataFrame
    df = pd.read_csv(file_path)

    # Remove \n and \r characters from the requirement_description column
    df['requirement_description'] = df['requirement_description'].str.replace(r'[\n\r]', '', regex=True)

    # Initialize an empty list to store each row as a comma-separated string
    rows = []
    rows.append("test_case_id,requirement_id,test_case_description,inputs,expected_outcome,requirement_description")
    # Iterate through each row and join values with commas, without spaces
    for _, row in df.iterrows():
        row_string = ','.join(str(value) for value in row)
        rows.append(row_string)

    # Join all rows with a newline character
    final_string = '\n'.join(rows)

    return final_string

def get_feedback_str(feedback_lst):
    """
    Converts feedback objects into a CSV-formatted string.

    Args:
        feedback_lst (list): List of feedback objects.

    Returns:
        str: A CSV-formatted string containing feedback information.
    """
    s = "test_case_id,feedback\n"
    for feedback in feedback_lst:
        s += feedback.test_case_id + "," + feedback.feedback + "\n"
    return s

def get_filtered_generated_test_cases(res_file, feedback_res):
    """
    Filters test cases based on feedback and returns them as a CSV-formatted string.

    Args:
        res_file (str): Path to the test cases CSV file.
        feedback_res (object): Feedback results containing test case IDs to filter.

    Returns:
        str: A CSV-formatted string of filtered test cases.
    """
    test_case_ids = []
    for feedback in feedback_res.feedback_list:
        test_case_ids.append(feedback.test_case_id)
    df = pd.read_csv(res_file)
    # Remove \n and \r characters from the requirement_description column
    df['requirement_description'] = df['requirement_description'].str.replace(r'[\n\r]', '', regex=True)

    # Initialize an empty list to store each row as a comma-separated string
    rows = []
    rows.append("test_case_id,requirement_id,test_case_description,inputs,expected_outcome,requirement_description")
    
    for _, row in df.iterrows():
        if row['test_case_id'] in test_case_ids:
            rows.append(f"{row['test_case_id']},{row['requirement_id']},{row['test_case_description']},{row['inputs']},{row['expected_outcome']},{row['requirement_description']}")
    
    # Join all rows into a single CSV-formatted string
    csv_output = "\n".join(rows)
    
    return csv_output

def replace_test_cases(corrected_cases, csv_file):
    """
    Replaces test cases in a CSV file with corrected cases.

    Args:
        corrected_cases (list): List of corrected test case objects.
        csv_file (str): Path to the test cases CSV file.

    Returns:
        None
    """
    # Load the CSV file into a DataFrame
    df = pd.read_csv(csv_file)
    
    # Convert corrected cases into a DataFrame
    corrected_df = pd.DataFrame([case.__dict__ for case in corrected_cases])
    
    # Iterate through each row in corrected_df to update matching rows in df
    for _, row in corrected_df.iterrows():
        test_case_id = row['test_case_id']
        
        # Check if the test_case_id exists in the CSV DataFrame
        if test_case_id in df['test_case_id'].values:
            # Locate the index of the matching row in the original DataFrame
            idx = df[df['test_case_id'] == test_case_id].index[0]
            
            # Update only the columns present in corrected_df for the matched row
            for col in corrected_df.columns:
                df.at[idx, col] = row[col]
    
    # Save the updated DataFrame back to the CSV file
    df.to_csv(csv_file, index=False)


# ---------------------Utils for Update system------------------------------------------------ #
def read_csv(file_path: str, unique_column: str, encoding: str = 'utf-8') -> Dict[str, Dict]:
    """
    Reads a CSV file and converts it into a dictionary using a unique column as the key.

    Args:
        file_path (str): Path to the CSV file.
        unique_column (str): Column to use as the dictionary key.
        encoding (str): Encoding of the CSV file. Defaults to 'utf-8'.

    Returns:
        dict: A dictionary with the unique column values as keys.
    """
    with open(file_path, 'r') as file:
        reader = csv.DictReader(file)
        data = {row[unique_column]: row for row in reader}
    return data

def get_diff(file1_path: str, file2_path: str, unique_column: str):
    """
    Compares two CSV files and identifies new, removed, and modified rows.

    Args:
        file1_path (str): Path to the first CSV file.
        file2_path (str): Path to the second CSV file.
        unique_column (str): Column used to identify rows.

    Returns:
        tuple: New rows, removed rows, and modified rows as dictionaries.
    """
    data1 = read_csv(file1_path, unique_column)
    data2 = read_csv(file2_path, unique_column)

    new_rows = {key: data2[key] for key in data2 if key not in data1}
    removed_rows = {key: data1[key] for key in data1 if key not in data2}
    modified_rows = {
        key: {'old_row': data1[key], 'new_row': data2[key]}
        for key in data1
        if key in data2 and data1[key] != data2[key]
    }

    return new_rows, removed_rows, modified_rows

def get_new_sections(file1_path: str, file2_path: str, unique_column: str) -> List[Dict]:
    """
    Identifies rows that are newly added in the second CSV file compared to the first.

    Args:
        file1_path (str): Path to the original CSV file.
        file2_path (str): Path to the updated CSV file.
        unique_column (str): Column name used to uniquely identify rows.

    Returns:
        List[Dict]: A list of dictionaries representing the new rows in the second CSV file.
    """
    new_rows, _, _ = get_diff(file1_path, file2_path, unique_column)
    return list(new_rows.values())

def get_removed_sections(file1_path: str, file2_path: str, unique_column: str) -> List[Dict]:
    """
    Identifies rows that are removed in the second CSV file compared to the first.

    Args:
        file1_path (str): Path to the original CSV file.
        file2_path (str): Path to the updated CSV file.
        unique_column (str): Column name used to uniquely identify rows.

    Returns:
        List[Dict]: A list of dictionaries representing the rows removed in the second CSV file.
    """
    _, removed_rows, _ = get_diff(file1_path, file2_path, unique_column)
    return list(removed_rows.values())

def get_changed_rows(file1_path: str, file2_path: str, unique_column: str) -> List[Dict]:
    """
    Identifies rows that have been modified between the two CSV files.

    Args:
        file1_path (str): Path to the original CSV file.
        file2_path (str): Path to the updated CSV file.
        unique_column (str): Column name used to uniquely identify rows.

    Returns:
        List[Dict]: A list of dictionaries containing 'old_row' and 'new_row' for modified rows.
    """
    _, _, modified_rows = get_diff(file1_path, file2_path, unique_column)
    return [
        {'Req ID': key, 'old_row': value['old_row'], 'new_row': value['new_row']}
        for key, value in modified_rows.items()
    ]

def remove_rows_by_req_id(input_file: str, req_ids_to_remove: list, unique_column: str = "Req ID"):
    """
    Removes rows with specified unique IDs from a CSV file.

    Args:
        input_file (str): Path to the input CSV file.
        req_ids_to_remove (list): List of unique IDs (e.g., Req IDs) to remove.
        unique_column (str): Column name used to identify rows to remove. Defaults to "Req ID".

    Returns:
        None
    """
    # Load the CSV into a DataFrame
    df = pd.read_csv(input_file)
    
    # Filter out rows with Req IDs in the list
    filtered_df = df[~df[unique_column].isin(req_ids_to_remove)]
    
    # Write the filtered DataFrame to the output file
    filtered_df.to_csv(input_file, index=False)

def df_to_custom_string(df):
    """
    Converts a DataFrame into a custom string format.

    Args:
        df (pd.DataFrame): The input DataFrame.

    Returns:
        str: A formatted string where each row's values are concatenated with column names.
    """
    result = ""
    # Iterate through rows and append each column's value
    for index, row in df.iterrows():
        for col in df.columns:
            # Ensure the value is a string and remove newlines/carriage returns
            value = str(row[col]).replace("\n", "").replace("\r", "")
            result += f"  {col}: {value}"
        result += "\n"
    return result

def changed_rows_to_str(changed_rows): 
    """
    Converts a list of changed rows into a formatted string.

    Args:
        changed_rows (list): A list of dictionaries containing 'old_row' and 'new_row' for each change.

    Returns:
        str: A formatted string showing old and new versions of the rows.
    """
    s = ""
    for row in changed_rows:
        s += "Old Requirement: " + "Req ID: " + row['old_row']['Req ID'] + ", Name: " + row['old_row']['Name'] + ", Description: " + row['old_row']['Description'].replace("\n", "").replace("\r", "")
        s += '\n'
        s += "New Requirement: " + "Req ID: " + row['new_row']['Req ID'] + ", Name: " + row['new_row']['Name'] + ", Description: " + row['new_row']['Description'].replace("\n", "").replace("\r", "")
    return s

def generate_updated_test_case_csv(pos_updated, neg_updated, edge_updated, output_file, req_desc_file):
    """
    Generates a CSV file containing updated test cases (positive, negative, and edge).

    Args:
        pos_updated (list): List of updated positive test cases.
        neg_updated (list): List of updated negative test cases.
        edge_updated (list): List of updated edge test cases.
        output_file (str): Path to save the generated CSV file.
        req_desc_file (str): Path to the requirements description CSV file.

    Returns:
        None
    """
    # Load requirement descriptions from the additional CSV file using pandas
    req_descriptions_df = pd.read_csv(req_desc_file)
    # Rename columns for consistency, if necessary
    req_descriptions_df.columns = ['requirement_id', 'Name', 'requirement_description']

    # Convert test cases to a pandas DataFrame
    test_case_data = []
    for idx, test_case in enumerate(pos_updated):
        test_case_id = f"p{test_case.requirement_id}_{idx + 1}"
        test_case_data.append({
            'test_case_id': test_case_id,
            'requirement_id': int(test_case.requirement_id),
            'test_case_description': test_case.test_case_description,
            'inputs': test_case.inputs,
            'expected_outcome': test_case.expected_outcome
        })
    for idx, test_case in enumerate(neg_updated):
        test_case_id = f"n{test_case.requirement_id}_{idx + 1}"
        test_case_data.append({
            'test_case_id': test_case_id,
            'requirement_id': int(test_case.requirement_id),
            'test_case_description': test_case.test_case_description,
            'inputs': test_case.inputs,
            'expected_outcome': test_case.expected_outcome
        })
    for idx, test_case in enumerate(edge_updated):
        test_case_id = f"e{test_case.requirement_id}_{idx + 1}"
        test_case_data.append({
            'test_case_id': test_case_id,
            'requirement_id': int(test_case.requirement_id),
            'test_case_description': test_case.test_case_description,
            'inputs': test_case.inputs,
            'expected_outcome': test_case.expected_outcome
        })

    test_cases_df = pd.DataFrame(test_case_data)

    # Merge the test cases DataFrame with the requirement descriptions DataFrame on requirement_id
    merged_df = pd.merge(test_cases_df, req_descriptions_df[['requirement_id', 'requirement_description']], 
                         on='requirement_id', how='left')

    # Fill in any missing descriptions with a default message (avoid inplace=True to avoid the warning)
    merged_df['requirement_description'] = merged_df['requirement_description'].fillna("No description available")

    # Save the merged DataFrame to CSV
    merged_df.to_csv(output_file, index=False)
    print(f"CSV file '{output_file}' has been created successfully.")


def generate_gen_updated_test_case_csv(updated_cases, output_file, req_desc_file):
    """
    Generates a CSV file for test cases with general updates.

    Args:
        updated_cases (list): List of updated test cases containing test type (positive, negative, or edge).
        output_file (str): Path to save the generated CSV file.
        req_desc_file (str): Path to the requirements description CSV file.

    Returns:
        None
    """
    # Load requirement descriptions from the additional CSV file using pandas
    req_descriptions_df = pd.read_csv(req_desc_file)
    # Rename columns for consistency, if necessary
    req_descriptions_df.columns = ['requirement_id', 'Name', 'requirement_description']

    # Convert test cases to a pandas DataFrame
    test_case_data = []
    for idx, test_case in enumerate(updated_cases):
        if test_case.test_type == 'positive':
            prefix = 'p'
        elif test_case.test_type == 'negative':
            prefix = 'n'
        else:
            prefix = 'e'
        test_case_id = f"{prefix}{test_case.requirement_id}_{idx + 1}"
        test_case_data.append({
            'test_case_id': test_case_id,
            'requirement_id': int(test_case.requirement_id),
            'test_case_description': test_case.test_case_description,
            'inputs': test_case.inputs,
            'expected_outcome': test_case.expected_outcome
        })
    test_cases_df = pd.DataFrame(test_case_data)

    # Merge the test cases DataFrame with the requirement descriptions DataFrame on requirement_id
    merged_df = pd.merge(test_cases_df, req_descriptions_df[['requirement_id', 'requirement_description']], 
                         on='requirement_id', how='left')

    # Fill in any missing descriptions with a default message (avoid inplace=True to avoid the warning)
    merged_df['requirement_description'] = merged_df['requirement_description'].fillna("No description available")

    # Save the merged DataFrame to CSV
    merged_df.to_csv(output_file, index=False)
    print(f"CSV file '{output_file}' has been created successfully.")
