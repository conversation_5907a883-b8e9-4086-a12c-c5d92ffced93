{"cells": [{"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import csv\n", "from typing import List, Dict\n", "import pandas as pd\n", "\n", "def read_csv(file_path: str, unique_column: str, encoding: str = 'utf-8') -> Dict[str, Dict]:\n", "    \"\"\"\n", "    Reads a CSV file and converts it into a dictionary with the unique column as the key.\n", "    \"\"\"\n", "    with open(file_path, 'r', encoding=encoding, errors='replace') as file:\n", "        reader = csv.DictReader(file)\n", "        data = {row[unique_column]: row for row in reader}\n", "    return data\n", "\n", "def get_diff(file1_path: str, file2_path: str, unique_column: str):\n", "    \"\"\"\n", "    Finds new, removed, and modified rows based on the unique column in the CSV files.\n", "    \"\"\"\n", "    data1 = read_csv(file1_path, unique_column)\n", "    data2 = read_csv(file2_path, unique_column)\n", "\n", "    new_rows = {key: data2[key] for key in data2 if key not in data1}\n", "    removed_rows = {key: data1[key] for key in data1 if key not in data2}\n", "    modified_rows = {\n", "        key: {'old_row': data1[key], 'new_row': data2[key]}\n", "        for key in data1\n", "        if key in data2 and data1[key] != data2[key]\n", "    }\n", "\n", "    return new_rows, removed_rows, modified_rows\n", "\n", "def get_new_sections(file1_path: str, file2_path: str, unique_column: str) -> List[Dict]:\n", "    \"\"\"\n", "    Returns rows that are newly added in file2.\n", "    \"\"\"\n", "    new_rows, _, _ = get_diff(file1_path, file2_path, unique_column)\n", "    return list(new_rows.values())\n", "\n", "def get_removed_sections(file1_path: str, file2_path: str, unique_column: str) -> List[Dict]:\n", "    \"\"\"\n", "    Returns rows that are removed in file2.\n", "    \"\"\"\n", "    _, removed_rows, _ = get_diff(file1_path, file2_path, unique_column)\n", "    return list(removed_rows.values())\n", "\n", "def get_changed_rows(file1_path: str, file2_path: str, unique_column: str) -> List[Dict]:\n", "    \"\"\"\n", "    Returns rows that are modified between file1 and file2.\n", "    \"\"\"\n", "    _, _, modified_rows = get_diff(file1_path, file2_path, unique_column)\n", "    return [\n", "        {'Req ID': key, 'old_row': value['old_row'], 'new_row': value['new_row']}\n", "        for key, value in modified_rows.items()\n", "    ]\n", "\n", "def remove_rows_by_req_id(input_file: str, req_ids_to_remove: list, unique_column: str = \"Req ID\"):\n", "    \"\"\"\n", "    Removes rows with specified Req IDs from a CSV file using pandas and writes the result to a new file.\n", "\n", "    Args:\n", "        input_file (str): Path to the input CSV file.\n", "        output_file (str): Path to the output CSV file.\n", "        req_ids_to_remove (list): List of Req IDs to remove.\n", "        unique_column (str): Name of the column used for identifying rows. Defaults to \"Req ID\".\n", "    \"\"\"\n", "    # Load the CSV into a DataFrame\n", "    df = pd.read_csv(input_file)\n", "    \n", "    # Filter out rows with Req IDs in the list\n", "    filtered_df = df[~df[unique_column].isin(req_ids_to_remove)]\n", "    \n", "    # Write the filtered DataFrame to the output file\n", "    filtered_df.to_csv(input_file, index=False)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# Create the two subsets of the data\n", "# data = pd.read_csv('../requirements.csv')\n", "# test1 = data.iloc[:5]  # First 5 rows\n", "# test2 = data.iloc[2:7]  # Rows 3, 4, 5, 6, and 7\n", "\n", "# # Save these subsets as CSV files\n", "test1_path = './update/test1.csv'\n", "test2_path = './update/test2.csv'\n", "\n", "# test1.to_csv(test1_path, index=False)\n", "# test2.to_csv(test2_path, index=False)\n", "\n", "# test1_path, test2_path"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["removed_sections = get_removed_sections(test1_path, test2_path, unique_column='Req ID')"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'Req ID': '480',\n", "  'Name': 'US Page Refresh',\n", "  'Description': 'Given: I have filled out the form fields and/or drop downs of either sensor replacement request form\\nWhen: I refresh the page\\nThen: the information I added remains in the fields, including any error states'},\n", " {'Req ID': '538',\n", "  'Name': 'US - Render Sensor Replacement Eligibility Page',\n", "  'Description': 'Given: User is on Guided Support Experience\\nWhen: User is navigated to Sensor Replacement Request from the Error message flow\\nThen: System will render eligibility page with following options:\\n\\nCheckbox to acknowledge privacy agreement\\nSubmit button\\n\\nRules list:\\nR1: User must agree to privacy acknowledgement prior to submit button becoming enabled\\nR2: If user clicks link at the end of the paragraph on sensitive data collection and usage, then they are directed to Abbott Privacy Resources'}]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["removed_sections"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["new_sections = get_new_sections(test1_path, test2_path, unique_column='Req ID')"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["changed_rows = get_changed_rows(test1_path, test2_path, unique_column='Req ID')"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["[480, 538]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["removed_ids = []\n", "for sec in removed_sections:\n", "    removed_ids.append(int(sec['Req ID']))\n", "\n", "removed_ids"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# remove_rows_by_req_id('./test_cases_test.csv', req_ids_to_remove=removed_ids, unique_column='requirement_id')"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'Req ID': '595',\n", "  'Name': 'US Field Validation - Phone Number',\n", "  'Description': 'Given: The User has accessed the Sensor Fell Off/Error message form page \\nWhen: The User interacts with the Phone Number field\\nThen: The system should show the validation and messaging including: \\n\\nNumeric values only\\n10 digit max\\nFocus away from field without entering a 10 digit phone number displays error state per the design with an error message to enter 10-digit phone number including area code with no hyphens or spaces.'},\n", " {'Req ID': '539',\n", "  'Name': 'US - Render Sensor Information Page',\n", "  'Description': 'Given: User is on Guided Support Experience\\nWhen: User acknowledges and submits eligibility form\\nThen: System will render sensor information page with following fields:\\n\\nSensor Serial Number\\nHow many days were you wearing the sensor?\\nIs your sensor available for return to Abbott?\\nWhat is the message event code?\\n\\nRules list:\\nR1. User must complete all required fields with valid entries prior to next button becoming enabled'}]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["new_sections"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["new_sections_df = pd.DataFrame(new_sections)\n", "new_sections_df.to_csv('./update/new_req.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Working on Updating the Requirements"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'Req ID': '534',\n", "  'old_row': {'Req ID': '534',\n", "   'Name': 'Sensor Serial Number - No Error',\n", "   'Description': 'Given User is on the page of the sensor error OR sensor fell off replacement form\\nWhen User is populating the sensor serial number field\\nThen it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.'},\n", "  'new_row': {'Req ID': '534',\n", "   'Name': 'Sensor Serial Number - Validation',\n", "   'Description': 'Given the user is on the sensor error or sensor replacement form page,\\nWhen the user populates the sensor serial number field,\\nThen the system should validate the input to ensure it consists of 9, 10, or 11 alphanumeric characters, excluding the letters “B”, “I”, “O”, and “S”. All characters will be automatically converted to and displayed in uppercase.'}}]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["changed_rows"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["[534]"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["changed_ids = []\n", "for row in changed_rows:\n", "    changed_ids.append(int(row['Req ID']))\n", "\n", "changed_ids"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>test_case_id</th>\n", "      <th>requirement_id</th>\n", "      <th>test_case_description</th>\n", "      <th>inputs</th>\n", "      <th>expected_outcome</th>\n", "      <th>requirement_description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>p534_4</td>\n", "      <td>534</td>\n", "      <td>Verify that the sensor serial number field acc...</td>\n", "      <td>Enter a valid sensor serial number (9, 10, or ...</td>\n", "      <td>The sensor serial number is accepted and displ...</td>\n", "      <td>Given User is on the page of the sensor error ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>n534_2</td>\n", "      <td>534</td>\n", "      <td>Verify that entering invalid characters ('B', ...</td>\n", "      <td>{\"sensor_serial_number\": \"12345B7890\"}</td>\n", "      <td>An error message is displayed indicating that ...</td>\n", "      <td>Given User is on the page of the sensor error ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>e534_2</td>\n", "      <td>534</td>\n", "      <td>Verify sensor serial number field handles maxi...</td>\n", "      <td>{\"sensor_serial_number\": \"*********B\"}</td>\n", "      <td>The system displays an error message indicatin...</td>\n", "      <td>Given User is on the page of the sensor error ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   test_case_id  requirement_id  \\\n", "3        p534_4             534   \n", "19       n534_2             534   \n", "29       e534_2             534   \n", "\n", "                                test_case_description  \\\n", "3   Verify that the sensor serial number field acc...   \n", "19  Verify that entering invalid characters ('B', ...   \n", "29  Verify sensor serial number field handles maxi...   \n", "\n", "                                               inputs  \\\n", "3   Enter a valid sensor serial number (9, 10, or ...   \n", "19             {\"sensor_serial_number\": \"12345B7890\"}   \n", "29             {\"sensor_serial_number\": \"*********B\"}   \n", "\n", "                                     expected_outcome  \\\n", "3   The sensor serial number is accepted and displ...   \n", "19  An error message is displayed indicating that ...   \n", "29  The system displays an error message indicatin...   \n", "\n", "                              requirement_description  \n", "3   Given User is on the page of the sensor error ...  \n", "19  Given User is on the page of the sensor error ...  \n", "29  Given User is on the page of the sensor error ...  "]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the old test cases for this\n", "data = pd.read_csv('./update/test_cases_test.csv')\n", "tests_to_change = data[data['requirement_id'].isin(changed_ids)]\n", "tests_to_change"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["def df_to_custom_string(df):\n", "    result = \"\"\n", "    # Iterate through rows and append each column's value\n", "    for index, row in df.iterrows():\n", "        for col in df.columns:\n", "            # Ensure the value is a string and remove newlines/carriage returns\n", "            value = str(row[col]).replace(\"\\n\", \"\").replace(\"\\r\", \"\")\n", "            result += f\"  {col}: {value}\"\n", "        result += \"\\n\"\n", "    return result\n", "res = df_to_custom_string(tests_to_change)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  test_case_id: p534_4  requirement_id: 534  test_case_description: Verify that the sensor serial number field accepts valid alphanumeric characters and lengths.  inputs: Enter a valid sensor serial number (9, 10, or 11 characters long, excluding 'B', 'I', 'O', and 'S').  expected_outcome: The sensor serial number is accepted and displayed in uppercase.  requirement_description: Given User is on the page of the sensor error OR sensor fell off replacement formWhen User is populating the sensor serial number fieldThen it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.\n", "  test_case_id: n534_2  requirement_id: 534  test_case_description: Verify that entering invalid characters ('B', 'I', 'O', 'S') in the sensor serial number field displays an error message.  inputs: {\"sensor_serial_number\": \"12345B7890\"}  expected_outcome: An error message is displayed indicating that the sensor serial number contains invalid characters.  requirement_description: Given User is on the page of the sensor error OR sensor fell off replacement formWhen User is populating the sensor serial number fieldThen it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.\n", "  test_case_id: e534_2  requirement_id: 534  test_case_description: Verify sensor serial number field handles maximum length input with valid characters except 'B', 'I', 'O', and 'S'.  inputs: {\"sensor_serial_number\": \"*********B\"}  expected_outcome: The system displays an error message indicating invalid characters.  requirement_description: Given User is on the page of the sensor error OR sensor fell off replacement formWhen User is populating the sensor serial number fieldThen it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.\n", "\n"]}], "source": ["print(res)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'Req ID': '534',\n", "  'old_row': {'Req ID': '534',\n", "   'Name': 'Sensor Serial Number - No Error',\n", "   'Description': 'Given User is on the page of the sensor error OR sensor fell off replacement form\\nWhen User is populating the sensor serial number field\\nThen it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.'},\n", "  'new_row': {'Req ID': '534',\n", "   'Name': 'Sensor Serial Number - Validation',\n", "   'Description': 'Given the user is on the sensor error or sensor replacement form page,\\nWhen the user populates the sensor serial number field,\\nThen the system should validate the input to ensure it consists of 9, 10, or 11 alphanumeric characters, excluding the letters “B”, “I”, “O”, and “S”. All characters will be automatically converted to and displayed in uppercase.'}}]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["changed_rows"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Old Requirement: Req ID: 534, Name: Sensor Serial Number - No Error, Description: Given User is on the page of the sensor error OR sensor fell off replacement formWhen User is populating the sensor serial number fieldThen it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.\n", "New Requirement: Req ID: 534, Name: Sensor Serial Number - Validation, Description: Given the user is on the sensor error or sensor replacement form page,When the user populates the sensor serial number field,Then the system should validate the input to ensure it consists of 9, 10, or 11 alphanumeric characters, excluding the letters “B”, “I”, “O”, and “S”. All characters will be automatically converted to and displayed in uppercase.\n"]}], "source": ["def changed_rows_to_str(changed_rows): \n", "    s = \"\"\n", "    for row in changed_rows:\n", "        s += \"Old Requirement: \" + \"Req ID: \" + row['old_row']['Req ID'] + \", Name: \" + row['old_row']['Name'] + \", Description: \" + row['old_row']['Description'].replace(\"\\n\", \"\").replace(\"\\r\", \"\")\n", "        s += '\\n'\n", "        s += \"New Requirement: \" + \"Req ID: \" + row['new_row']['Req ID'] + \", Name: \" + row['new_row']['Name'] + \", Description: \" + row['new_row']['Description'].replace(\"\\n\", \"\").replace(\"\\r\", \"\")\n", "    return s\n", "\n", "changed_req = changed_rows_to_str(changed_rows)\n", "print(changed_req)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["# Imports\n", "import json \n", "from langchain_core.prompts import ChatPromptTemplate, PromptTemplate\n", "from pydantic import BaseModel, Field\n", "from typing import List, Dict\n", "from langchain_openai import AzureChatOpenAI\n", "from langchain_core.output_parsers import StrOutputParser\n", "from typing_extensions import TypedDict\n", "from IPython.display import Image\n", "from pprint import pprint\n", "import os\n", "from langchain_community.document_loaders.csv_loader import CSVLoader\n", "from dotenv import load_dotenv\n", "import pandas as pd\n", "import csv\n", "import re\n", "import io\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["TestCases(test_cases=[TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field automatically converts lowercase input to uppercase and accepts valid alphanumeric characters and lengths.', inputs=\"Enter a valid sensor serial number in lowercase (e.g., 'a23456789').\", expected_outcome='The sensor serial number is automatically converted to uppercase and accepted.'), TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field accepts valid alphanumeric characters and lengths without any invalid characters.', inputs=\"Enter a valid sensor serial number (9, 10, or 11 characters long, excluding 'B', 'I', 'O', and 'S').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.'), TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles minimum length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 9 characters (e.g., '*********').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.'), TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles maximum length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 11 characters (e.g., '*********C1').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.'), TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles mid-range length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 10 characters (e.g., '*********C').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.')])"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["### Positive test case chain\n", "# Pydantic for structured output\n", "class TestCase(BaseModel):\n", "    requirement_id: str = Field(description=\"Requirement Id from the csv\")\n", "    # requirement_description: str = Field(description=\"Requirement text from the csv\")\n", "    test_case_description: str = Field(description=\"Test case that validates the requirement\")\n", "    inputs: str = Field(description=\"List of inputs for the test case\")\n", "    expected_outcome: str = Field(description=\"Expected result of the test case\")\n", "\n", "class TestCases(BaseModel):\n", "    test_cases: List[TestCase] = Field(description=\"A list of the test cases\")\n", "\n", "\n", "pos_update_tst_prompt_template = \"\"\"\n", "You are an LLM agent responsible for generating new positive test cases for a project based on updates in requirements. The task involves analyzing the updated requirements and previously generated test cases to produce a list of new positive test cases. Your goal is to ensure that the test cases accurately reflect the updated requirements.\n", "\n", "Follow these guidelines:\n", "1. Carefully analyze the updated requirements and previously generated test cases.\n", "2. Identify gaps or changes in the test coverage due to the updated requirements and generate new positive test cases.\n", "3. Ensure each new test case is detailed, specifying inputs, expected outcomes, and any relevant setup.\n", "4. Focus solely on positive test cases that validate the correct and expected functionality of the project according to the updated requirements.\n", "\n", "Format each new test case in this structure:\n", "- Requirement ID: [ID from CSV or reference string]\n", "- Test Case Description: [Test case that validates the requirement]\n", "- Inputs: [List of inputs for the test case]\n", "- Expected Outcome: [Expected result of the test case]\n", "\n", "Example for a new test case:\n", "Requirement ID: 001\n", "Test Case Description: \"Verify successful login with updated password policy (minimum 12 characters).\"\n", "Inputs: {{\"username\": \"validUser\", \"password\": \"ValidPass1234\"}}\n", "Expected Outcome: \"User is successfully logged in and redirected to the dashboard.\"\n", "\n", "Here is the input data for the task:\n", "- Updated Requirements: {changed_req}\n", "- Old Test Cases: {old_test_cases}\n", "\"\"\"\n", "\n", "\n", "pos_tst_prompt = PromptTemplate(input_variables=['changed_req', 'old_test_cases'], template=pos_update_tst_prompt_template)\n", "\n", "# Initialize the llm\n", "llm = AzureChatOpenAI(model='gpt-4o', temperature=0)\n", "structured_llm = llm.with_structured_output(TestCases)\n", "pos_generator = pos_tst_prompt | structured_llm\n", "\n", "# Test it out\n", "res = pos_generator.invoke({\"changed_req\": changed_req, \"old_test_cases\": res})\n", "res"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["len(res.test_cases)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["[TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field automatically converts lowercase input to uppercase and accepts valid alphanumeric characters and lengths.', inputs=\"Enter a valid sensor serial number in lowercase (e.g., 'a23456789').\", expected_outcome='The sensor serial number is automatically converted to uppercase and accepted.'),\n", " TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field accepts valid alphanumeric characters and lengths without any invalid characters.', inputs=\"Enter a valid sensor serial number (9, 10, or 11 characters long, excluding 'B', 'I', 'O', and 'S').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.'),\n", " TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles minimum length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 9 characters (e.g., '*********').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.'),\n", " TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles maximum length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 11 characters (e.g., '*********C1').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.'),\n", " TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles mid-range length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 10 characters (e.g., '*********C').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.')]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["res.test_cases"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["TestCases(test_cases=[TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input with invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'B' (e.g., 'A234B6789').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\"), TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input with invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'I' (e.g., 'A234I6789').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\"), TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input with invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'O' (e.g., 'A234O6789').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\"), TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input with invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'S' (e.g., 'A234S6789').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\"), TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input shorter than 9 characters.', inputs=\"Enter a sensor serial number with 8 characters (e.g., '********').\", expected_outcome=\"The system displays an error message: 'Sensor serial number must be 9, 10, or 11 characters long.'\"), TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input longer than 11 characters.', inputs=\"Enter a sensor serial number with 12 characters (e.g., '*********C12').\", expected_outcome=\"The system displays an error message: 'Sensor serial number must be 9, 10, or 11 characters long.'\"), TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input with special characters.', inputs=\"Enter a sensor serial number with special characters (e.g., '********@').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\"), TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input with spaces.', inputs=\"Enter a sensor serial number with spaces (e.g., 'A234 6789').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\")])"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["pos_update_tst_prompt_template = \"\"\"\n", "You are an LLM agent responsible for generating new positive test cases for a project based on updates in requirements. The task involves analyzing the updated requirements and previously generated test cases to produce a list of new positive test cases. Your goal is to ensure that the test cases accurately reflect the updated requirements.\n", "\n", "Follow these guidelines:\n", "1. Carefully analyze the updated requirements and previously generated test cases.\n", "2. Identify gaps or changes in the test coverage due to the updated requirements and generate new positive test cases.\n", "3. Ensure each new test case is detailed, specifying inputs, expected outcomes, and any relevant setup.\n", "4. Focus solely on positive test cases that validate the correct and expected functionality of the project according to the updated requirements.\n", "\n", "Format each new test case in this structure:\n", "- Requirement ID: [ID from CSV or reference string]\n", "- Test Case Description: [Test case that validates the requirement]\n", "- Inputs: [List of inputs for the test case]\n", "- Expected Outcome: [Expected result of the test case]\n", "\n", "Example for a new test case:\n", "Requirement ID: 001\n", "Test Case Description: \"Verify successful login with updated password policy (minimum 12 characters).\"\n", "Inputs: {{\"username\": \"validUser\", \"password\": \"ValidPass1234\"}}\n", "Expected Outcome: \"User is successfully logged in and redirected to the dashboard.\"\n", "\n", "Here is the input data for the task:\n", "- Updated Requirements: {changed_req}\n", "- Old Test Cases: {old_test_cases}\n", "\"\"\"\n", "\n", "\n", "pos_tst_prompt = PromptTemplate(input_variables=['changed_req', 'old_test_cases'], template=pos_update_tst_prompt_template)\n", "\n", "# Initialize the llm\n", "llm = AzureChatOpenAI(model='gpt-4o', temperature=0)\n", "structured_llm = llm.with_structured_output(TestCases)\n", "pos_generator = pos_tst_prompt | structured_llm\n", "\n", "# Test it out\n", "neg_res = neg_generator.invoke({\"changed_req\": changed_req, \"old_test_cases\": res})\n", "neg_res"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["8"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["len(neg_res.test_cases)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["[TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input with invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'B' (e.g., 'A234B6789').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\"),\n", " TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input with invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'I' (e.g., 'A234I6789').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\"),\n", " TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input with invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'O' (e.g., 'A234O6789').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\"),\n", " TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input with invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'S' (e.g., 'A234S6789').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\"),\n", " TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input shorter than 9 characters.', inputs=\"Enter a sensor serial number with 8 characters (e.g., '********').\", expected_outcome=\"The system displays an error message: 'Sensor serial number must be 9, 10, or 11 characters long.'\"),\n", " TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input longer than 11 characters.', inputs=\"Enter a sensor serial number with 12 characters (e.g., '*********C12').\", expected_outcome=\"The system displays an error message: 'Sensor serial number must be 9, 10, or 11 characters long.'\"),\n", " TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input with special characters.', inputs=\"Enter a sensor serial number with special characters (e.g., '********@').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\"),\n", " TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input with spaces.', inputs=\"Enter a sensor serial number with spaces (e.g., 'A234 6789').\", expected_outcome=\"The system displays an error message: 'Invalid character in sensor serial number.'\")]"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["neg_res.test_cases"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["TestCases(test_cases=[TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input with invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'B', 'I', 'O', or 'S' (e.g., 'A23B56789').\", expected_outcome=\"The system displays an error message indicating that the characters 'B', 'I', 'O', and 'S' are not allowed.\"), TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input shorter than 9 characters.', inputs=\"Enter a sensor serial number with 8 characters (e.g., '********').\", expected_outcome='The system displays an error message indicating that the serial number must be 9, 10, or 11 characters long.'), TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input longer than 11 characters.', inputs=\"Enter a sensor serial number with 12 characters (e.g., '*********C12').\", expected_outcome='The system displays an error message indicating that the serial number must be 9, 10, or 11 characters long.'), TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field automatically converts mixed case input to uppercase.', inputs=\"Enter a sensor serial number with mixed case characters (e.g., 'aBcDeFgHiJ').\", expected_outcome='The sensor serial number is automatically converted to uppercase and accepted.'), TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field handles input with special characters and rejects it.', inputs=\"Enter a sensor serial number with special characters (e.g., 'A2345@789').\", expected_outcome='The system displays an error message indicating that only alphanumeric characters are allowed.')])"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["edge_update_tst_prompt_template = \"\"\"\n", "You are an LLM agent responsible for generating new edge test cases for a project based on updates in requirements. The task involves analyzing the updated requirements and previously generated test cases to produce a list of new edge test cases. Your goal is to ensure that the test cases accurately reflect the system's behavior under boundary or extreme conditions.\n", "\n", "Follow these guidelines:\n", "1. Carefully analyze the updated requirements and previously generated test cases.\n", "2. Identify gaps or changes in the test coverage due to the updated requirements and generate new edge test cases.\n", "3. Ensure each new test case is detailed, specifying boundary or extreme inputs, expected outcomes, and any relevant setup.\n", "4. Focus solely on edge test cases that validate the system's behavior under edge conditions.\n", "\n", "Format each new test case in this structure:\n", "- Requirement ID: [ID from CSV or reference string]\n", "- Test Case Description: [Test case that validates the requirement under boundary or extreme conditions]\n", "- Inputs: [List of boundary or extreme inputs for the test case]\n", "- Expected Outcome: [Expected result of the test case]\n", "\n", "Example for a new test case:\n", "Requirement ID: 002\n", "Test Case Description: \"Verify system behavior when the username exceeds the maximum allowed length.\"\n", "Inputs: {{\"username\": \"a\" * 101, \"password\": \"ValidPass1234\"}}  # Username exceeds 100 characters\n", "Expected Outcome: \"The system displays an error message: 'Username cannot exceed 100 characters.'\"\n", "\n", "Here is the input data for the task:\n", "- Updated Requirements: {changed_req}\n", "- Old Test Cases: {old_test_cases}\n", "\"\"\"\n", "\n", "edge_tst_prompt = PromptTemplate(input_variables=['changed_req', 'old_test_cases'], template=edge_update_tst_prompt_template)\n", "\n", "# Initialize the llm\n", "edge_generator = edge_tst_prompt | structured_llm\n", "\n", "# Test it out\n", "edge_res = edge_generator.invoke({\"changed_req\": changed_req, \"old_test_cases\": res})\n", "edge_res"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["len(edge_res.test_cases)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["[TestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input with invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'B', 'I', 'O', or 'S' (e.g., 'A23B56789').\", expected_outcome=\"The system displays an error message indicating that the characters 'B', 'I', 'O', and 'S' are not allowed.\"),\n", " TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input shorter than 9 characters.', inputs=\"Enter a sensor serial number with 8 characters (e.g., '********').\", expected_outcome='The system displays an error message indicating that the serial number must be 9, 10, or 11 characters long.'),\n", " TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input longer than 11 characters.', inputs=\"Enter a sensor serial number with 12 characters (e.g., '*********C12').\", expected_outcome='The system displays an error message indicating that the serial number must be 9, 10, or 11 characters long.'),\n", " TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field automatically converts mixed case input to uppercase.', inputs=\"Enter a sensor serial number with mixed case characters (e.g., 'aBcDeFgHiJ').\", expected_outcome='The sensor serial number is automatically converted to uppercase and accepted.'),\n", " TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field handles input with special characters and rejects it.', inputs=\"Enter a sensor serial number with special characters (e.g., 'A2345@789').\", expected_outcome='The system displays an error message indicating that only alphanumeric characters are allowed.')]"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["edge_res.test_cases"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Trying General prompt"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["GenTestCases(test_cases=[GenTestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field automatically converts lowercase input to uppercase and accepts valid alphanumeric characters and lengths.', inputs=\"Enter a valid sensor serial number in lowercase (e.g., 'a23456789').\", expected_outcome='The sensor serial number is automatically converted to uppercase and accepted.', test_type='positive'), GenTestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field accepts valid alphanumeric characters and lengths without any invalid characters.', inputs=\"Enter a valid sensor serial number (9, 10, or 11 characters long, excluding 'B', 'I', 'O', and 'S').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.', test_type='positive'), GenTestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles minimum length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 9 characters (e.g., '*********').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.', test_type='edge'), GenTestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles maximum length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 11 characters (e.g., '*********C1').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.', test_type='edge'), GenTestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles mid-range length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 10 characters (e.g., '*********C').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.', test_type='positive'), GenTestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input containing invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'B', 'I', 'O', or 'S' (e.g., 'A23B56789').\", expected_outcome='The sensor serial number is rejected and an error message is displayed.', test_type='negative'), GenTestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input shorter than 9 characters.', inputs=\"Enter a sensor serial number with 8 characters (e.g., '********').\", expected_outcome='The sensor serial number is rejected and an error message is displayed.', test_type='negative'), GenTestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input longer than 11 characters.', inputs=\"Enter a sensor serial number with 12 characters (e.g., '*********C12').\", expected_outcome='The sensor serial number is rejected and an error message is displayed.', test_type='negative')])"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["class GenTestCase(BaseModel):\n", "    requirement_id: str = Field(description=\"Requirement Id from the csv\")\n", "    test_case_description: str = Field(description=\"Test case that validates the requirement\")\n", "    inputs: str = Field(description=\"List of inputs for the test case\")\n", "    expected_outcome: str = Field(description=\"Expected result of the test case\")\n", "    test_type: str = Field(description=\"Type of the test case: positive, negative, or edge\")  # New field\n", "\n", "class GenTestCases(BaseModel):\n", "    test_cases: List[GenTestCase] = Field(description=\"A list of the test cases\")\n", "\n", "\n", "general_test_case_prompt_template = \"\"\"\n", "You are an LLM agent responsible for generating test cases (positive, negative, and edge) for a project based on updates in requirements. The task involves analyzing the updated requirements and previously generated test cases to produce a comprehensive list of test cases. Your goal is to ensure that the test cases accurately reflect the updated requirements.\n", "\n", "Follow these guidelines:\n", "1. Carefully analyze the updated requirements and previously generated test cases.\n", "2. Identify gaps or changes in test coverage due to the updated requirements and generate new test cases for the following types:\n", "   - Positive test cases: Validate the correct and expected functionality.\n", "   - Negative test cases: Validate system behavior with invalid or unexpected inputs.\n", "   - Edge test cases: Validate system behavior under boundary or extreme conditions.\n", "3. Ensure each test case is detailed, specifying inputs, expected outcomes, and any relevant setup.\n", "4. Clearly specify the test type for each test case (positive, negative, or edge).\n", "\n", "Format each test case in this structure:\n", "- Requirement ID: [ID from CSV or reference string]\n", "- Test Case Description: [Test case that validates the requirement]\n", "- Inputs: [List of inputs for the test case]\n", "- Expected Outcome: [Expected result of the test case]\n", "- Test Type: [positive, negative, or edge]\n", "\n", "Example test cases:\n", "Requirement ID: 001\n", "Test Case Description: \"Verify successful login with correct username and password.\"\n", "Inputs: {{\"username\": \"validUser\", \"password\": \"validPass123\"}}\n", "Expected Outcome: \"User is successfully logged in and redirected to the dashboard.\"\n", "Test Type: positive\n", "\n", "Requirement ID: 002\n", "Test Case Description: \"Verify error message when login is attempted with a password shorter than the required minimum length.\"\n", "Inputs: {{\"username\": \"validUser\", \"password\": \"short\"}}\n", "Expected Outcome: \"The system displays an error message: 'Password must be at least 12 characters long.'\"\n", "Test Type: negative\n", "\n", "Requirement ID: 003\n", "Test Case Description: \"Verify system behavior when the username exceeds the maximum allowed length.\"\n", "Inputs: {{\"username\": \"a\" * 101, \"password\": \"ValidPass1234\"}}  # Username exceeds 100 characters\n", "Expected Outcome: \"The system displays an error message: 'Username cannot exceed 100 characters.'\"\n", "Test Type: edge\n", "\n", "Here is the input data for the task:\n", "- Updated Requirements: {changed_req}\n", "- Old Test Cases: {old_test_cases}\n", "\"\"\"\n", "\n", "general_tst_prompt = PromptTemplate(input_variables=['changed_req', 'old_test_cases'], template=general_test_case_prompt_template)\n", "\n", "# Initialize the llm\n", "llm = AzureChatOpenAI(model='gpt-4o', temperature=0)\n", "structured_llm = llm.with_structured_output(GenTestCases)\n", "general_generator = general_tst_prompt | structured_llm\n", "\n", "# Test it out\n", "general_res = general_generator.invoke({\"changed_req\": changed_req, \"old_test_cases\": res})\n", "general_res"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/plain": ["8"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["len(general_res.test_cases)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["[GenTestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field automatically converts lowercase input to uppercase and accepts valid alphanumeric characters and lengths.', inputs=\"Enter a valid sensor serial number in lowercase (e.g., 'a23456789').\", expected_outcome='The sensor serial number is automatically converted to uppercase and accepted.', test_type='positive'),\n", " GenTestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field accepts valid alphanumeric characters and lengths without any invalid characters.', inputs=\"Enter a valid sensor serial number (9, 10, or 11 characters long, excluding 'B', 'I', 'O', and 'S').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.', test_type='positive'),\n", " GenTestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles minimum length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 9 characters (e.g., '*********').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.', test_type='edge'),\n", " GenTestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles maximum length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 11 characters (e.g., '*********C1').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.', test_type='edge'),\n", " GenTestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field handles mid-range length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a valid sensor serial number with 10 characters (e.g., '*********C').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.', test_type='positive'),\n", " GenTestCase(requirement_id='534', test_case_description=\"Verify that the sensor serial number field rejects input containing invalid characters 'B', 'I', 'O', and 'S'.\", inputs=\"Enter a sensor serial number containing 'B', 'I', 'O', or 'S' (e.g., 'A23B56789').\", expected_outcome='The sensor serial number is rejected and an error message is displayed.', test_type='negative'),\n", " GenTestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input shorter than 9 characters.', inputs=\"Enter a sensor serial number with 8 characters (e.g., '********').\", expected_outcome='The sensor serial number is rejected and an error message is displayed.', test_type='negative'),\n", " GenTestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field rejects input longer than 11 characters.', inputs=\"Enter a sensor serial number with 12 characters (e.g., '*********C12').\", expected_outcome='The sensor serial number is rejected and an error message is displayed.', test_type='negative')]"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["general_res.test_cases"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generate the csv file"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV file 'updated_cases.csv' has been created successfully.\n"]}], "source": ["def generate_updated_test_case_csv(pos_updated, neg_updated, edge_updated, output_file='updated_cases.csv', req_desc_file='../test2.csv'):\n", "\n", "    # Load requirement descriptions from the additional CSV file using pandas\n", "    req_descriptions_df = pd.read_csv(req_desc_file)\n", "    # Rename columns for consistency, if necessary\n", "    req_descriptions_df.columns = ['requirement_id', 'Name', 'requirement_description']\n", "\n", "    # Convert test cases to a pandas DataFrame\n", "    test_case_data = []\n", "    for idx, test_case in enumerate(pos_updated):\n", "        test_case_id = f\"p{test_case.requirement_id}_{idx + 1}\"\n", "        test_case_data.append({\n", "            'test_case_id': test_case_id,\n", "            'requirement_id': int(test_case.requirement_id),\n", "            'test_case_description': test_case.test_case_description,\n", "            'inputs': test_case.inputs,\n", "            'expected_outcome': test_case.expected_outcome\n", "        })\n", "    for idx, test_case in enumerate(neg_updated):\n", "        test_case_id = f\"n{test_case.requirement_id}_{idx + 1}\"\n", "        test_case_data.append({\n", "            'test_case_id': test_case_id,\n", "            'requirement_id': int(test_case.requirement_id),\n", "            'test_case_description': test_case.test_case_description,\n", "            'inputs': test_case.inputs,\n", "            'expected_outcome': test_case.expected_outcome\n", "        })\n", "    for idx, test_case in enumerate(edge_updated):\n", "        test_case_id = f\"e{test_case.requirement_id}_{idx + 1}\"\n", "        test_case_data.append({\n", "            'test_case_id': test_case_id,\n", "            'requirement_id': int(test_case.requirement_id),\n", "            'test_case_description': test_case.test_case_description,\n", "            'inputs': test_case.inputs,\n", "            'expected_outcome': test_case.expected_outcome\n", "        })\n", "\n", "    test_cases_df = pd.DataFrame(test_case_data)\n", "\n", "    # Merge the test cases DataFrame with the requirement descriptions DataFrame on requirement_id\n", "    merged_df = pd.merge(test_cases_df, req_descriptions_df[['requirement_id', 'requirement_description']], \n", "                         on='requirement_id', how='left')\n", "\n", "    # Fill in any missing descriptions with a default message (avoid inplace=True to avoid the warning)\n", "    merged_df['requirement_description'] = merged_df['requirement_description'].fillna(\"No description available\")\n", "\n", "    # Save the merged DataFrame to CSV\n", "    merged_df.to_csv(output_file, index=False)\n", "    print(f\"CSV file '{output_file}' has been created successfully.\")\n", "    \n", "generate_updated_test_case_csv(res.test_cases, neg_res.test_cases, edge_res.test_cases, output_file='updated_cases.csv', req_desc_file='./update/test2.csv')"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV file 'updated_gen_cases.csv' has been created successfully.\n"]}], "source": ["def generate_gen_updated_test_case_csv(updated_cases, output_file='updated_gen_cases.csv', req_desc_file='test2.csv'):\n", "    # Load requirement descriptions from the additional CSV file using pandas\n", "    req_descriptions_df = pd.read_csv(req_desc_file)\n", "    # Rename columns for consistency, if necessary\n", "    req_descriptions_df.columns = ['requirement_id', 'Name', 'requirement_description']\n", "\n", "    # Convert test cases to a pandas DataFrame\n", "    test_case_data = []\n", "    for idx, test_case in enumerate(updated_cases):\n", "        if test_case.test_type == 'positive':\n", "            prefix = 'p'\n", "        elif test_case.test_type == 'negative':\n", "            prefix = 'n'\n", "        else:\n", "            prefix = 'e'\n", "        test_case_id = f\"{prefix}{test_case.requirement_id}_{idx + 1}\"\n", "        test_case_data.append({\n", "            'test_case_id': test_case_id,\n", "            'requirement_id': int(test_case.requirement_id),\n", "            'test_case_description': test_case.test_case_description,\n", "            'inputs': test_case.inputs,\n", "            'expected_outcome': test_case.expected_outcome\n", "        })\n", "    test_cases_df = pd.DataFrame(test_case_data)\n", "\n", "    # Merge the test cases DataFrame with the requirement descriptions DataFrame on requirement_id\n", "    merged_df = pd.merge(test_cases_df, req_descriptions_df[['requirement_id', 'requirement_description']], \n", "                         on='requirement_id', how='left')\n", "\n", "    # Fill in any missing descriptions with a default message (avoid inplace=True to avoid the warning)\n", "    merged_df['requirement_description'] = merged_df['requirement_description'].fillna(\"No description available\")\n", "\n", "    # Save the merged DataFrame to CSV\n", "    merged_df.to_csv(output_file, index=False)\n", "    print(f\"CSV file '{output_file}' has been created successfully.\")\n", "    \n", "generate_gen_updated_test_case_csv(general_res.test_cases, output_file='updated_gen_cases.csv', req_desc_file='./update/test2.csv')"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Graph"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [], "source": ["class UpdateGraphState(TypedDict):\n", "    old_csv_file_path: str\n", "    new_csv_file_path: str\n", "    old_test_cases_file_path: str\n", "    output_file_path: str\n", "    type_gen: str\n", "    rem_filtered_path: str\n", "    new_test_path: str\n", "    update_test_path: str\n", "    removed_sections: List[Dict]\n", "    new_sections: List[Dict]\n", "    changed_rows: List[Dict]"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [], "source": ["from graph import create_graph_workflow"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [], "source": ["### Nodes\n", "def check_differences(state):\n", "    # Set the paths for the updated files as None\n", "    rem_filtered_path = None\n", "    new_test_path = None\n", "    update_test_path = None\n", "    print(\"---CHECKING DIFFERENCES IN THE FILES---\")\n", "    # Get the paths for the old and new file\n", "    old_csv_file_path = state['old_csv_file_path']\n", "    new_csv_file_path = state['new_csv_file_path']\n", "\n", "    # Check the removed sections\n", "    removed_sections = get_removed_sections(old_csv_file_path, new_csv_file_path, \n", "                                            unique_column='Req ID')\n", "    # Get the new sections (requirements that were added)\n", "    new_sections = get_new_sections(old_csv_file_path, new_csv_file_path, \n", "                                    unique_column='Req ID')\n", "    # Get the requirements that were updated\n", "    changed_rows = get_changed_rows(old_csv_file_path, new_csv_file_path, \n", "                                    unique_column='Req ID')\n", "    \n", "    return {\"removed_sections\": removed_sections, \"new_sections\": new_sections, \n", "            \"changed_rows\": changed_rows, \"rem_filtered_path\": rem_filtered_path,\n", "            \"new_test_path\": new_test_path, \"update_test_path\": update_test_path}\n", "\n", "def remove_test_cases(state):\n", "    print(\"---CHECKING IF REQUIREMENTS WERE REMOVED---\")\n", "    # Get the removed sections list\n", "    removed_sections = state['removed_sections']\n", "    # Check if there are no removed sections\n", "    if removed_sections == []:\n", "        print(\"---DECISION: NO REQUIREMENTS WERE REMOVED---\")\n", "        return {\"rem_filtered_path\": state['old_test_cases_file_path']}\n", "    else:\n", "        print(\"---DECISION: DELETING TEST CASES FOR THE REMOVED REQUIREMENTS---\")\n", "        # Getting the ids to remove \n", "        removed_ids = []\n", "        for sec in removed_sections:\n", "            removed_ids.append(int(sec['Req ID']))\n", "\n", "        # Get the path of the test_cases file\n", "        old_test_cases_file_path = state['old_test_cases_file_path']\n", "        df = pd.read_csv(old_test_cases_file_path)\n", "        filtered_df = df[~df['requirement_id'].isin(removed_ids)]\n", "        rem_filtered_path = './update/removed_tests.csv'\n", "        filtered_df.to_csv(rem_filtered_path, index=False)\n", "        print(f\"Filtered file saved at: {rem_filtered_path}\")\n", "        return {\"rem_filtered_path\": rem_filtered_path}\n", "    return \n", "\n", "def new_test_cases(state):\n", "    print(\"---CHECKING IF THERE ARE NEW REQUIREMENTS---\")\n", "    # Get the new sections list\n", "    new_sections = state['new_sections']\n", "    if new_sections == []:\n", "        print(\"---DECISION: NO NEW SECTIONS WERE FOUND---\")\n", "        return \n", "    else:\n", "        print(\"---DECISION: NEW REQUIREMENTS WERE FOUND, GENERATING NEW TEST CASES---\")\n", "        # Convert the list of dicts to a df for processing by the generate test cases graph\n", "        new_sections_df = pd.DataFrame(new_sections)\n", "        new_sections_df.to_csv('./update/new_req.csv', index=False)\n", "        # Create the app and run the graph\n", "        app = create_graph_workflow()\n", "        new_test_path = './update/new_test_cases.csv'\n", "        # Define the inputs  \n", "        inputs = {\"csv_file_path\": './update/new_req.csv', 'output_file_path': new_test_path}\n", "        # Run the graph\n", "        for output in app.stream(inputs):\n", "            for key, value in output.items():\n", "                # Node\n", "                pass\n", "        print(f\"New test cases file saved at: {new_test_path}\")\n", "        # Delete the requirements file as it is no longer needed\n", "        os.remove('./update/new_req.csv')\n", "        return {\"new_test_path\": new_test_path}\n", "\n", "def update_test_cases(state):\n", "    print(\"---CHECKING IF REQUIREMENTS WERE UPDATED---\")\n", "    changed_rows = state['changed_rows']\n", "    if changed_rows == []:\n", "        print('---DECSION: NO REQUIREMENTS WERE UPDATED---')\n", "        return\n", "    else:\n", "        print(\"---DECISION: UPDATING REQUIREMENTS---\")\n", "        # get the old test_cases_csv file\n", "        old_test_cases_file_path = state['old_test_cases_file_path']\n", "        data = pd.read_csv(old_test_cases_file_path)\n", "        tests_to_change = data[data['requirement_id'].isin(changed_ids)]\n", "\n", "        # Convert this to string\n", "        res = df_to_custom_string(tests_to_change)\n", "        # Convert changed reqs to string\n", "        changed_req = changed_rows_to_str(changed_rows)\n", "\n", "        # Get the type of generation\n", "        type_gen = state['type_gen']\n", "\n", "        # Get the output file path\n", "        update_test_path = './update/updated_tests.csv'\n", "        new_csv_file_path = state['new_csv_file_path']\n", "\n", "        if type_gen == 'general':\n", "            print('---GENERATING ALL TEST CASES AT ONCE---')\n", "            general_res = general_generator.invoke({\"changed_req\": changed_req, \"old_test_cases\": res})\n", "            generate_gen_updated_test_case_csv(general_res.test_cases, \n", "                                               output_file=update_test_path, \n", "                                               req_desc_file=new_csv_file_path)\n", "            return {\"update_test_path\": update_test_path}\n", "        else:\n", "            res = pos_generator.invoke({\"changed_req\": changed_req, \"old_test_cases\": res})\n", "            neg_res = neg_generator.invoke({\"changed_req\": changed_req, \"old_test_cases\": res})\n", "            edge_res = edge_generator.invoke({\"changed_req\": changed_req, \"old_test_cases\": res})\n", "\n", "            generate_updated_test_case_csv(res.test_cases, neg_res.test_cases, \n", "                                           edge_res.test_cases, output_file=update_test_path, \n", "                                           req_desc_file=new_csv_file_path)\n", "            return {\"update_test_path\": update_test_path}\n", "def finalize_csv(state):\n", "    # Get the file paths of all three operations\n", "    rem_filtered_path = state['rem_filtered_path']\n", "    new_test_path = state['new_test_path']\n", "    update_test_path = state['update_test_path']\n", "    file_paths = [rem_filtered_path, new_test_path, update_test_path]\n", "\n", "    if all(path is None for path in file_paths):\n", "        print(\"No Changes were required.\")\n", "        return \n", "    else:\n", "        dfs = []\n", "        for path in file_paths:\n", "            if path is not None:\n", "                print(f\"Processing file {path}\")\n", "                df = pd.read_csv(path)\n", "                dfs.append(df)\n", "                # Delete the csv file\n", "                os.remove(path)\n", "                print(f\"File deleted: {path}\")\n", "        combined_df = pd.concat(dfs, ignore_index=True)\n", "        # updated_file_path = './update/updated_test_cases.csv'\n", "        output_file_path = state['output_file_path']\n", "        combined_df.to_csv(output_file_path, index=False)\n", "        print(f\"Files combined and saved as: {output_file_path}\")\n", "        return {\"output_file_path\": output_file_path}"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Build Graph"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph, START\n", "\n", "update_workflow = StateGraph(UpdateGraphState)\n", "\n", "# Define the nodes\n", "update_workflow.add_node(\"check_differences\", check_differences)\n", "update_workflow.add_node(\"remove_test_cases\", remove_test_cases)\n", "update_workflow.add_node(\"new_test_cases\", new_test_cases)\n", "update_workflow.add_node(\"update_test_cases\", update_test_cases)\n", "update_workflow.add_node(\"finalize_csv\", finalize_csv)\n", "\n", "# Build the graph\n", "update_workflow.add_edge(START, 'check_differences')\n", "update_workflow.add_edge('check_differences', 'remove_test_cases')\n", "update_workflow.add_edge('check_differences', 'new_test_cases')\n", "update_workflow.add_edge('check_differences', 'update_test_cases')\n", "update_workflow.add_edge('remove_test_cases', 'finalize_csv')\n", "update_workflow.add_edge('new_test_cases', 'finalize_csv')\n", "update_workflow.add_edge('update_test_cases', 'finalize_csv')\n", "\n", "update_app = update_workflow.compile()"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"data": {"text/plain": ["Graph(nodes={'__start__': Node(id='__start__', name='__start__', data=<class 'langchain_core.utils.pydantic.LangGraphInput'>, metadata=None), 'check_differences': Node(id='check_differences', name='check_differences', data=check_differences(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'remove_test_cases': Node(id='remove_test_cases', name='remove_test_cases', data=remove_test_cases(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'new_test_cases': Node(id='new_test_cases', name='new_test_cases', data=new_test_cases(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'update_test_cases': Node(id='update_test_cases', name='update_test_cases', data=update_test_cases(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'finalize_csv': Node(id='finalize_csv', name='finalize_csv', data=finalize_csv(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None)}, edges=[Edge(source='__start__', target='check_differences', data=None, conditional=False), Edge(source='check_differences', target='new_test_cases', data=None, conditional=False), Edge(source='check_differences', target='remove_test_cases', data=None, conditional=False), Edge(source='check_differences', target='update_test_cases', data=None, conditional=False), Edge(source='new_test_cases', target='finalize_csv', data=None, conditional=False), Edge(source='remove_test_cases', target='finalize_csv', data=None, conditional=False), Edge(source='update_test_cases', target='finalize_csv', data=None, conditional=False)])"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["update_app.get_graph()"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [], "source": ["from pprint import pprint"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---CHECKING DIFFERENCES IN THE FILES---\n", "\"Node 'check_differences':\"\n", "'\\n---\\n'\n", "---CHECKING IF REQUIREMENTS WERE REMOVED---\n", "---DECISION: DELETING TEST CASES FOR THE REMOVED REQUIREMENTS---\n", "---CHECKING IF THERE ARE NEW REQUIREMENTS---\n", "---DECISION: NEW REQUIREMENTS WERE FOUND, GENERATING NEW TEST CASES---\n", "---CHECKING IF REQUIREMENTS WERE UPDATED---\n", "---DECISION: UPDATING REQUIREMENTS---\n", "---LOADING REQUIREMENTS CSV FILE---\n", "---GENERATING ALL TEST CASES AT ONCE---\n", "---GENERATING POSITIVE TEST CASES---\n", "---GENERATING NEGATIVE TEST CASES---\n", "---GENERATING EDGE TEST CASES---\n", "Filtered file saved at: ./update/removed_tests.csv\n", "\"Node 'remove_test_cases':\"\n", "'\\n---\\n'\n", "CSV file './test_cases/negative_test_cases.csv' has been created successfully.\n", "CSV file './test_cases/positive_test_cases.csv' has been created successfully.\n", "CSV file './update/updated_tests.csv' has been created successfully.\n", "\"Node 'update_test_cases':\"\n", "'\\n---\\n'\n", "CSV file './test_cases/edge_test_cases.csv' has been created successfully.\n", "---GENERATING FEEDBACK FOR POSITIVE TEST CASES---\n", "---GENERATING FEED<PERSON>CK FOR <PERSON>GATIVE TEST CASES---\n", "---GENERATING FEEDBACK FOR EDGE TEST CASES---\n", "---CORRECTING POSITIVE TEST CASES BASED ON FEEDBACK------CORRECTING NEGATIVE TEST CASES BASED ON FEEDBACK---\n", "\n", "---CORRECTING <PERSON>D<PERSON> TEST CASES BASED ON FEEDBACK---\n", "---COMBINING THE CSV FILES---\n", "New test cases file saved at: ./update/new_test_cases.csv\n", "\"Node 'new_test_cases':\"\n", "'\\n---\\n'\n", "Processing file ./update/removed_tests.csv\n", "File deleted: ./update/removed_tests.csv\n", "Processing file ./update/new_test_cases.csv\n", "File deleted: ./update/new_test_cases.csv\n", "Processing file ./update/updated_tests.csv\n", "File deleted: ./update/updated_tests.csv\n", "Files combined and saved as: ./update/updated_test_cases.csv\n", "\"Node 'finalize_csv':\"\n", "'\\n---\\n'\n", "'./update/updated_test_cases.csv'\n"]}], "source": ["# Test out the graph\n", "inputs = {\"old_csv_file_path\": './update/test1.csv', \n", "          'new_csv_file_path': './update/test2.csv',\n", "          'old_test_cases_file_path': './update/test_cases_test.csv',\n", "          'output_file_path': './update/updated_test_cases.csv', \n", "          'type_gen': 'general'}\n", "\n", "for output in update_app.stream(inputs):\n", "    for key, value in output.items():\n", "        # Node\n", "        pprint(f\"Node '{key}':\")\n", "    pprint(\"\\n---\\n\")\n", "pprint(value['output_file_path'])"]}], "metadata": {"kernelspec": {"display_name": "cap", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 2}