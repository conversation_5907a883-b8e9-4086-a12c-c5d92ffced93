<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="96" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="HtmlUnknownTag" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="7">
            <item index="0" class="java.lang.String" itemvalue="nobr" />
            <item index="1" class="java.lang.String" itemvalue="noembed" />
            <item index="2" class="java.lang.String" itemvalue="comment" />
            <item index="3" class="java.lang.String" itemvalue="noscript" />
            <item index="4" class="java.lang.String" itemvalue="embed" />
            <item index="5" class="java.lang.String" itemvalue="script" />
            <item index="6" class="java.lang.String" itemvalue="langflow-chat" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="false" level="WARNING" enabled_by_default="false">
      <option name="ignoredPackages">
        <value>
          <list size="16">
            <item index="0" class="java.lang.String" itemvalue="clyent" />
            <item index="1" class="java.lang.String" itemvalue="TBB" />
            <item index="2" class="java.lang.String" itemvalue="conda-verify" />
            <item index="3" class="java.lang.String" itemvalue="conda" />
            <item index="4" class="java.lang.String" itemvalue="conda-build" />
            <item index="5" class="java.lang.String" itemvalue="llvmlite" />
            <item index="6" class="java.lang.String" itemvalue="datashape" />
            <item index="7" class="java.lang.String" itemvalue="pyopenssl" />
            <item index="8" class="java.lang.String" itemvalue="pandas2.2.0" />
            <item index="9" class="java.lang.String" itemvalue="PyPDF23.0.1" />
            <item index="10" class="java.lang.String" itemvalue="pillow10.3.0" />
            <item index="11" class="java.lang.String" itemvalue="PyYAML6.0.1" />
            <item index="12" class="java.lang.String" itemvalue="PyMuPDFb1.24.3" />
            <item index="13" class="java.lang.String" itemvalue="requests2.32.1" />
            <item index="14" class="java.lang.String" itemvalue="langfuse2.32.0" />
            <item index="15" class="java.lang.String" itemvalue="langflow1.0.0a34" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N801" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="ShellCheck" enabled="true" level="ERROR" enabled_by_default="true">
      <shellcheck_settings value="SC1048,SC1073" />
    </inspection_tool>
    <inspection_tool class="SqlNoDataSourceInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="UnsatisfiedRequirementInspection" enabled="true" level="INFORMATION" enabled_by_default="true" />
  </profile>
</component>