import pysqlite3
import sys
sys.modules["sqlite3"] = sys.modules.pop("pysqlite3")
import chromadb

from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
from pprint import pprint
from graph import create_graph_workflow

import warnings
import traceback

# Suppress the specific warning
warnings.filterwarnings("ignore", message="Relevance scores must be between 0 and 1")


app = Flask(__name__)
CORS(app)

@app.route('/')
def home():
    """
    Render the home page of the application.

    Returns:
        str: The HTML content of the home page.
    """
    return render_template('index.html')  # Serve the main web page

@app.route('/get_answer', methods=['POST'])
def answer_question():
    """
    Process a POST request to answer a user's question.

    The function expects JSON input with the key 'question' and uses a Retrieval-Augmented Generation (RAG) system
    to generate a response. The response includes the generated answer, document references, and optional messages.

    Returns:
        flask.Response: A JSON response containing the generated answer or an error message.
    """
    try:
        print('method is called')
        data = request.json
        question = data.get('question', '')
        if not question:
            return jsonify({"error": "No question provided"}), 400
        
        # Create the app
        rag = create_graph_workflow()
        # Define the inputs  
        inputs = {
            "query": question
        }

        # Initialize variables to store the outputs
        generation = None
        documents = []
        msg = None

        for output in rag.stream(inputs):
            for key, value in output.items():
                if 'document_sources' in value:
                    documents = value['document_sources']
                if 'generation' in value:
                    generation = value['generation']
                if 'msg' in value:
                    msg = value['msg']
                pprint(f"Node: '{key}': ")
            pprint("\n--\n")

        if generation == None:
            return jsonify({"answer": 'The answer to this question does not exist in the system!'})
        elif msg == None or msg == '':
            return jsonify({"answer": "".join([generation, "\r\n\r\nRef: ", str(documents)])})
        else:
            return jsonify({"answer": "".join([generation, "\r\n\r\nRef: ", str(documents), "\r\n\r\nMessage: ", msg])})

    except Exception as e:
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    """
    Entry point for the Flask application. Runs the app in debug mode.
    """
    app.run(debug=True)