from pprint import pprint
from graph import create_graph_workflow, create_update_graph_workflow
from dotenv import load_dotenv
import json
import os

def generate_test_cases(input_file_path, output_file_path):
    # Create the app
    app = create_graph_workflow()
    # Define the inputs  
    inputs = {"csv_file_path": "./requirements.csv", 'output_file_path': './test_cases.csv'}
    for output in app.stream(inputs):
        for key, value in output.items():
            # Node
            pprint(f"Node '{key}':")
        pprint("\n---\n")

def update():
    inputs = {"old_csv_file_path": './update/test1.csv', 
          'new_csv_file_path': './update/test2.csv',
          'old_test_cases_file_path': './update/test_cases_test.csv',
          'output_file_path': './update/updated_test_cases.csv', 
          'type_gen': 'general'}
    
    update_app = create_update_graph_workflow()

    for output in update_app.stream(inputs):
        for key, value in output.items():
            # Node
            pprint(f"Node '{key}':")
        pprint("\n---\n")
    pprint(value['output_file_path'])

def main(gen_or_update='gen'):
    if gen_or_update == 'gen':
        input_file_path = './requirements.csv'
        output_file_path = './test_cases.csv'
        generate_test_cases(input_file_path, output_file_path)
    else:
        update()

if __name__ == '__main__':
    main()