# Imports
from rank_bm25 import BM25Okapi

# LangChain BM25 retriever
class BM25Retriever:
    """
    A retriever class using the BM25 algorithm to rank and retrieve the most relevant documents for a given query.

    Attributes:
        chunks (list): A list of documents (or chunks of text) to search through.
        bm25 (BM25Okapi): An instance of the BM25Okapi model initialized with tokenized documents.
    """

    def __init__(self, chunks):
        """
        Initialize the BM25Retriever with a set of documents (chunks).

        Args:
            chunks (list): A list of document objects, where each object contains the attribute `page_content` 
                           representing the text content of the document.
        """
        self.chunks = chunks
        tokenized_docs = [doc.page_content.split(" ") for doc in chunks]
        self.bm25 = BM25Okapi(tokenized_docs)

    def get_relevant_documents(self, query, k=3):
        """
        Retrieve the top-k most relevant documents for a given query.

        Args:
            query (str): The search query as a string.
            k (int, optional): The number of top documents to retrieve. Defaults to 3.

        Returns:
            tuple: A tuple containing:
                - relevant_docs (list): The top-k relevant documents as a list of objects from `self.chunks`.
                - scores (list): The corresponding BM25 scores for the retrieved documents.
        """
        query_tokenized = query.split(" ")
        scores = self.bm25.get_scores(query_tokenized)
        ranked_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)
        relevant_docs = [self.chunks[i] for i in ranked_indices]
        scores = [scores[i] for i in ranked_indices]
        return relevant_docs[:k], scores[:k]