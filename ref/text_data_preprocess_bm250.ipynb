{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !py -m pip install dotenv\n", "# !pip install azure-storage-blob\n", "# !pip install azure-identity\n", "# !pip install tiktoken langchain langgraph\n", "# !pip install python-dotenv\n", "# !pip install langchain_openai\n", "# !pip install langchain_community\n", "# !pip install rank_bm25"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "from azure.storage.blob import BlobServiceClient, BlobClient, ContainerClient\n", "from azure.identity import DefaultAzureCredential\n", "import tiktoken\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "# Load the .env file\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get the connection string and the container name\n", "connection_string = os.getenv(\"AZURE_CONNECTION_STRING\")\n", "container_name = os.getenv(\"CONTAINER_NAME\")\n", "token_credential = DefaultAzureCredential()\n", "os.environ['AZURE_OPENAI_API_KEY'] = os.getenv(\"AZURE_OPENAI_API_KEY\")\n", "os.environ['AZURE_OPENAI_ENDPOINT'] = os.getenv(\"AZURE_OPENAI_ENDPOINT\")\n", "os.environ['OPENAI_API_VERSION'] = os.getenv(\"OPENAI_API_VERSION\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blob_service_client = BlobServiceClient.from_connection_string(str(connection_string))\n", "container_client = blob_service_client.get_container_client(container_name)\n", "\n", "# Get all the blobs from the container\n", "blobs = container_client.list_blobs()\n", "\n", "text_dict = {}\n", "for blob in blobs:\n", "    blob_client = container_client.get_blob_client(blob.name)\n", "    download_stream = blob_client.download_blob()\n", "    file_content = download_stream.readall().decode('utf-8')\n", "    #Store in the dictionary\n", "    text_dict[str(blob.name)] = file_content\n", "    \n", "print(f\"Number of files in the container: {len(text_dict)}\")\n", "print(f\"Name of the files: {text_dict.keys()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Printing the number of words in each file\n", "for key, value in text_dict.items():\n", "    num_words = len(text_dict[key])\n", "    print(f\"File Name {str(key)}, Num Words: {num_words}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function that prints some token statistics of the text data\n", "def print_num_tokens(model_name, text_dict):\n", "    token_num_dict = {}\n", "    encoding = tiktoken.encoding_for_model(model_name)\n", "    for file_name, text in text_dict.items():\n", "        text_tokens = encoding.encode(text)\n", "        print(f\"File name: {file_name}, No. of Tokens: {len(text_tokens)}\")\n", "        token_num_dict[str(file_name)] = len(text_tokens)\n", "    # Average number of token\n", "    num_token_lst = list(token_num_dict.values())\n", "    avg_tokens = sum(num_token_lst) / len(num_token_lst)\n", "    print(f\"Average number of tokens in file: {avg_tokens}\")\n", "    print(f\"Max number of tokens in a file: {max(num_token_lst)}\")\n", "    print(f\"Min number of token in a file: {min(num_token_lst)}\")\n", "    \n", "    # Find file names with min and max tokens\n", "    min_tokens_file = min(token_num_dict, key=token_num_dict.get)\n", "    max_tokens_file = max(token_num_dict, key=token_num_dict.get)\n", "    print(f\"File with minimum tokens: {min_tokens_file}\")\n", "    print(f\"File with maximum tokens: {max_tokens_file}\")\n", "\n", "# Assuming that we will be using gpt-4o\n", "print_num_tokens(model_name='gpt-4o', text_dict=text_dict)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Preprocessing data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Removing the headers and the details about\n", "def remove_unused_info(text: str) -> str:\n", "    marker = \"END OF DOCUMENT\"\n", "    # Find the position of the end of document\n", "    pos = text.find(marker)\n", "    if pos != -1:\n", "        text = text[:pos + len(marker)]\n", "    else:\n", "        print(\"END OF DOCUMENT NOT FOUND\")\n", " \n", "    start_marker = \"Abbott – Business and Technology Services\"\n", "    end_marker = \"This information is confidential to <PERSON>. The user is responsible for using the appropriate version of this document.\"\n", " \n", "    start_marker_pos = text.find(start_marker)\n", "    end_marker_pos = text.find(end_marker)\n", "    while start_marker_pos != -1 and end_marker_pos > start_marker_pos and \"Document Type\" in text[start_marker_pos:end_marker_pos]:\n", "        text = text[:start_marker_pos] + ' ' + text[end_marker_pos + len(end_marker):] \n", "        start_marker_pos = text.find(start_marker)\n", "        end_marker_pos = text.find(end_marker)\n", "    return text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for file_name, text in text_dict.items():\n", "    text_dict[file_name] = remove_unused_info(text=text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print_num_tokens(model_name='gpt-4o', text_dict=text_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Save the processed text files locally\n", "# import os\n", "\n", "# # Specify the folder where you want to save the files\n", "# output_folder = 'text_files'\n", "\n", "# # Create the folder if it doesn't exist\n", "# if not os.path.exists(output_folder):\n", "#     os.makedirs(output_folder)\n", "\n", "# # Save each file in text_dict as a .txt file\n", "# for key, value in text_dict.items():\n", "#     file_path = os.path.join(output_folder, f\"{key}\")\n", "#     with open(file_path, 'w', encoding='utf-8') as file:\n", "#         file.write(value)\n", "\n", "# print(f\"All files have been saved in the folder: {output_folder}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# # Load the text files as text_dict\n", "# # Specify the folder where the text files are stored\n", "# input_folder = 'text_files'\n", "\n", "# # Initialize an empty dictionary to store the file contents\n", "# text_dict = {}\n", "\n", "# # Iterate through each file in the folder\n", "# for file_name in os.listdir(input_folder):\n", "#     if file_name.endswith('.txt'):\n", "#         file_path = os.path.join(input_folder, file_name)\n", "#         with open(file_path, 'r', encoding='utf-8') as file:\n", "#             content = file.read()\n", "#             # Use the file name (without extension) as the key\n", "#             key = os.path.splitext(file_name)[0]\n", "#             text_dict[key] = content\n", "\n", "# print(\"All files have been loaded into text_dict.\")\n", "# print(len(text_dict))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Setting up Azure Chat OpenAI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_openai import AzureChatOpenAI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# llm = AzureChatOpenAI(\n", "#     azure_deployment=\"teamcapstonechat\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# messages = [\n", "#     (\n", "#         \"system\",\n", "#         \"You translate from English to French.\"\n", "#     ),\n", "#     (\"human\", \"I love programming.\"),\n", "# ]\n", "# res = llm.invoke(messages)\n", "# res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# llm.invoke(\"Tell me a joke\").content"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Contextual Retrieval (Anthropic)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "import hashlib\n", "import os\n", "import getpass\n", "from typing import List, <PERSON><PERSON>\n", "from dotenv import load_dotenv\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain.schema import Document\n", "from langchain.vectorstores import Chroma, FAISS\n", "from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings\n", "from langchain.prompts import ChatPromptTemplate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import openai\n", "\n", "class ContextualRetrieval:\n", "    def __init__(self):\n", "        self.text_splitter = RecursiveCharacterTextSplitter(\n", "            chunk_size=1300,\n", "            chunk_overlap=200,\n", "        )\n", "        self.llm = AzureChatOpenAI(\n", "            azure_deployment=\"teamcapstonechat\",\n", "            temperature=0,\n", "        )\n", "\n", "    def process_document(self, document):\n", "        chunks = self.text_splitter.create_documents([document])\n", "        contextualized_chunks = self._get_contextualized_chunks(document, chunks)\n", "        return chunks, contextualized_chunks\n", "\n", "    def _generate_context(self, document, chunk):\n", "        prompt_template = \"\"\"\n", "        <document>\n", "        {document}\n", "        </document>\n", "        Here is a chunk we want to situate within the whole document\n", "        <chunk>\n", "        {chunk}\n", "        </chunk>\n", "        Please give a short succinct context to situate this chunk within the\\\n", "        overall document for the purposes of improving search retrieval of the chunk.\\\n", "        Answer only with the succinct context and nothing else.\n", "        \"\"\"\n", "        prompt = ChatPromptTemplate.from_template(template=prompt_template)\n", "        messages = prompt.format_messages(document=document, chunk=chunk)\n", "        \n", "        # Exponential backoff\n", "        max_retries = 5\n", "        retry_delay = 1  # initial delay in seconds\n", "        for attempt in range(max_retries):\n", "            try:\n", "                response = self.llm.invoke(messages)\n", "                return response.content\n", "            except openai.RateLimitError as e:\n", "                if attempt < max_retries - 1:\n", "                    time.sleep(retry_delay)\n", "                    retry_delay *= 2  # Exponentially increase the delay\n", "                else:\n", "                    raise e  # Raise the error if max retries are exceeded\n", "\n", "    def _get_contextualized_chunks(self, document, chunks):\n", "        contextualized_chunks = []\n", "        for chunk in chunks:\n", "            context = self._generate_context(document, chunk.page_content)\n", "            contextualized_content = f\"{context}\\n\\n{chunk.page_content}\"\n", "            contextualized_chunks.append(Document(page_content=contextualized_content, \n", "                                                  metadata=chunk.metadata))\n", "        return contextualized_chunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["contextualized_files = \"contextualized_files.txt\"\n", "\n", "if not os.path.exists(contextualized_files):\n", "    with open(contextualized_files, 'w') as file:\n", "        pass\n", "\n", "with open(contextualized_files, \"r\") as file:\n", "    contextualized_file_names = file.read()\n", "\n", "# Specify the folder where you want to save the files\n", "C_output_folder = 'contextualized_chunks_files'\n", "\n", "# Create the folder if it doesn't exist\n", "if not os.path.exists(C_output_folder):\n", "     os.makedirs(C_output_folder)\n", "\n", "C_retrieval = ContextualRetrieval()\n", "\n", "for file_name, text in text_dict.items():\n", "    if file_name in contextualized_file_names:\n", "        continue\n", "\n", "    # Process the document\n", "    _, contextualized_chunks = C_retrieval.process_document(document=text)\n", "    file_path = os.path.join(C_output_folder, f\"{file_name}\")\n", "    with open(file_path, 'w', encoding='utf-8') as file:\n", "      file.writelines(f\"{item}\\n\" for item in contextualized_chunks)\n", "    with open(contextualized_files, 'a', encoding='utf-8') as file:\n", "      file.write(\" \"  + file_name)\n", "    print(\"Processed: \" + file_name)\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(original_chunks[0].page_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(contextualized_chunks[0].page_content)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### BM25 keyword ranking implementation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_community.retrievers.bm25 import BM25Retriever\n", "from rank_bm25 import BM25Okapi\n", "from langchain.schema import Document\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> BM25 retriever\n", "class BM25Retriever:\n", "    def __init__(self, bm25, split_docs_flat):\n", "        self.bm25 = bm25\n", "        self.split_docs_flat = split_docs_flat\n", "\n", "    def get_relevant_documents(self, query, k=3):\n", "        query_tokenized = query.split(\" \")\n", "        scores = self.bm25.get_scores(query_tokenized)\n", "        ranked_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)\n", "        relevant_docs = [self.split_docs_flat[i] for i in ranked_indices]\n", "        scores = [scores[i] for i in ranked_indices]\n", "        return relevant_docs[:k], scores[:k]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create your corpus of documents.\n", "documents = [\n", "    Document(page_content=\"LangChain provides a framework for building language model applications.\"),\n", "    Document(page_content=\"BM25 is a popular ranking algorithm used in information retrieval.\"),\n", "    Document(page_content=\"Python is a versatile programming language.\"),\n", "    Document(page_content=\"Artificial Intelligence is transforming many industries.\"),\n", "]\n", "\n", "# Use RecursiveCharacterTextSplitter to split documents into chunks\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1300, chunk_overlap=200)\n", "split_docs = [text_splitter.split_text(doc.page_content) for doc in documents]\n", "\n", "# Flatten the split document list\n", "split_docs_flat = [chunk for doc_chunks in split_docs for chunk in doc_chunks]\n", "\n", "# Tokenize the documents for BM25 (using BM25Okapi)\n", "tokenized_docs = [doc.split(\" \") for doc in split_docs_flat]\n", "\n", "# Initialize BM25Okapi retriever\n", "bm25 = BM25Okapi(tokenized_docs)\n", "\n", "# Define a query and retrieve relevant documents\n", "query = \"framework algorithm\"\n", "bm25_retriever = BM25Retriever(bm25, split_docs_flat)\n", "relevant_docs, _ = bm25_retriever.get_relevant_documents(query)\n", "\n", "# Display results\n", "for i, doc in enumerate(relevant_docs):\n", "    print(f\"Document {i+1}: {doc}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Getting it to work with text_dict\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size = 2000, chunk_overlap = 200)\n", "chunks = []\n", "for filename, text in text_dict.items():\n", "    file_chunks = text_splitter.create_documents([text], metadatas=[{\"source\": filename}])\n", "    for file_chunk in file_chunks: \n", "        chunks.append(file_chunk)\n", "\n", "# Tokenize the documents for BM25 (using BM25Okapi)\n", "tokenized_docs = [doc.page_content.split(\" \") for doc in chunks]\n", "\n", "# Initialize BM25Okapi retriever\n", "bm25 = BM25Okapi(tokenized_docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define the query and retrieve relevant documents\n", "query = \"PCS Archer Support Team\"\n", "\n", "bm25_retriever = BM25Retriever(bm25, chunks)\n", "relevant_docs, scores = bm25_retriever.get_relevant_documents(query, k=5)\n", "relevant_docs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scores"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Creating a Chroma Vector Store"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "from langchain_chroma import Chroma\n", "from langchain_community.vectorstores import FAISS\n", "# from langchain_huggingface import HuggingFaceEmbeddings\n", "from langchain_openai import AzureOpenAIEmbeddings\n", "from uuid import uuid4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test with hugging face\n", "# Define the path to the pre-trained model you want to use\n", "# modelPath = \"sentence-transformers/all-MiniLM-l6-v2\"\n", "\n", "# # Create a dictionary with model configuration options, specifying to use the CPU for computations\n", "# model_kwargs = {'device':'cpu'}\n", "\n", "# # Create a dictionary with encoding options, specifically setting 'normalize_embeddings' to False\n", "# encode_kwargs = {'normalize_embeddings': False}\n", "\n", "# # Initialize an instance of HuggingFaceEmbeddings with the specified parameters\n", "# embeddings = HuggingFaceEmbeddings(\n", "#     model_name=modelPath,     # Provide the pre-trained model's path\n", "#     model_kwargs=model_kwargs, # Pass the model configuration options\n", "#     encode_kwargs=encode_kwargs # Pass the encoding options\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.environ['AZURE_OPENAI_ENDPOINT'] = os.getenv(\"AZURE_OPENAI_EMBEDDING_ENDPOINT\")\n", "os.environ['OPENAI_API_VERSION'] = os.getenv(\"AZURE_EMBEDDING_API_VERSION\")\n", "embeddings = AzureOpenAIEmbeddings(model='text-embedding-3-small')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chunks[0].metadata"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(chunks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["type(chunks[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uuids = [str(uuid4()) for _ in range(len(chunks))]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# validation\n", "texts = [doc.page_content for doc in chunks]\n", "metadatas = [doc.metadata for doc in chunks]\n", "\n", "# Ensure texts are strings and metadatas are dictionaries\n", "assert all(isinstance(text, str) for text in texts), \"All texts must be strings\"\n", "assert all(isinstance(metadata, dict) for metadata in metadatas), \"All metadatas must be dictionaries\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(uuids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# db = FAISS.from_documents(documents=documents,embedding=embeddings)\n", "# db.save_local(\"./faiss-db\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# question = \"What is Python?\"\n", "# searchDocs = db.similarity_search(question)\n", "# print(searchDocs[0].page_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import openai\n", "\n", "# Set up exponential backoff parameters\n", "def retry_with_backoff(api_call, *args, max_retries=5, initial_wait=1, **kwargs):\n", "    retries = 0\n", "    wait_time = initial_wait\n", "    while retries < max_retries:\n", "        try:\n", "            return api_call(*args, **kwargs)\n", "        except openai.RateLimitError as e:\n", "            print(f\"Rate limit exceeded. Retrying in {wait_time} seconds...\")\n", "            time.sleep(wait_time)\n", "            retries += 1\n", "            wait_time *= 2  # Exponential backoff\n", "            \n", "    raise Exception(\"Max retries exceeded\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "# Initialize the vector store\n", "vector_store = Chroma(\n", "    collection_name='bts-slc-db',\n", "    embedding_function=embeddings,\n", "    persist_directory='./chroma_db_new'\n", ")\n", "\n", "vector_store.persist()\n", "\n", "def embed_and_store_text_chunks(file_paths):\n", "    embedding_processed_files = \"embedding_processed_files.txt\"\n", "\n", "    if not os.path.exists(embedding_processed_files):\n", "        with open(embedding_processed_files, 'w') as file:\n", "            pass\n", "\n", "    with open(embedding_processed_files, \"r\") as file:\n", "        embedding_processed_file_paths = file.read()\n", "\n", "    for file_path in file_paths:\n", "        if file_path in embedding_processed_file_paths:\n", "                continue\n", "\n", "        with open(file_path, \"r\", encoding='utf-8') as f:\n", "            text = f.read()\n", "\n", "        # Split text into chunks\n", "        chunks = text_splitter.split_text(text)\n", "\n", "        # Embed each chunk with exponential backoff\n", "        _embeddings = []\n", "        for chunk in chunks:\n", "            embedding_result = retry_with_backoff(lambda: embeddings.embed_documents(chunk))\n", "            _embeddings.append(embedding_result)\n", "\n", "        # Add embedded chunks to the vector store\n", "        vector_store.add_documents(documents=chunks, embeddings=_embeddings)\n", "\n", "        with open(embedding_processed_files, 'a', encoding='utf-8') as file:\n", "            file.write(\" \"  + file_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from os import listdir\n", "from os.path import isfile, join\n", "\n", "file_paths = []\n", "for f in listdir(\"contextualized_chunks_files\"):\n", "   if isfile((join(\"./contextualized_chunks_files\", f))):\n", "        file_paths.append(\"./contextualized_chunks_files/\" + f)\n", "\n", "embed_and_store_text_chunks(file_paths)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "slc-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}