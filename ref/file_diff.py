import difflib
from typing import Iterator

def get_diff(file1_path:str, file2_path:str) -> Iterator[str]:
    # Read the content of both files
    with open(file1_path, 'r') as file1, open(file2_path, 'r') as file2:
        content1 = file1.readlines()
        content2 = file2.readlines()

    # Find the differences
    diff = difflib.unified_diff(
        content1,
        content2,
        fromfile=file1_path,
        tofile=file2_path,
        lineterm=''
    )

    return diff

def get_new_sections(file1_path:str, file2_path:str) -> list[str]:
    diff = get_diff(file1_path, file2_path)

    # Filter the newly added ones only
    new_sections = [line[1:] for line in diff if line.startswith('+') and not line.startswith('+++')]

    return new_sections

def get_removed_sections(file1_path:str, file2_path:str) -> list[str]:
    diff = get_diff(file1_path, file2_path)

    # Filter the removed ones only
    removed_sections = [line[1:] for line in diff if line.startswith('-') and not line.startswith('---')]

    return removed_sections


# Example usages:
lines = ["Requirement 1\n", "Requirement 2\n", "Requirement 3\n"]

with open('file1.txt', 'w') as file:
    file.writelines(lines)

print("### test new lines at the end")
lines = ["Requirement 1\n", "Requirement 2\n", "Requirement 3\n", "Requirement 4\n", "Requirement 5\n"]
with open('file2.txt', 'w') as file:
    file.writelines(lines)

new_sections =  get_new_sections('file1.txt', 'file2.txt')
for line in new_sections:
    print(line)

print("### test new lines in the middle of existing")
lines =  ["Requirement 4\n", "Requirement 1\n", "Requirement 2\n", "Requirement 5\n",  "Requirement 3\n"]
with open('file2.txt', 'w') as file:
    file.writelines(lines)

new_sections =  get_new_sections('file1.txt', 'file2.txt')
for line in new_sections:
    print(line)

print("### test removed sections")
lines =  ["Requirement 1\n", "Requirement 3\n", "Requirement 4\n", "Requirement 5\n"]
with open('file2.txt', 'w') as file:
    file.writelines(lines)

removed_sections =  get_removed_sections('file1.txt', 'file2.txt')
for line in removed_sections:
    print(line[1:])

print('## test no difference')
assert(not get_new_sections('file1.txt', 'file1.txt'))
assert(not get_removed_sections('file2.txt', 'file2.txt'))


