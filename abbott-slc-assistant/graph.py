# Imports
from typing_extensions import TypedDict
from typing import List
from langgraph.graph import END, StateGraph, START
from nodes import *


# Define the Graph state
class GraphState(TypedDict):
    """
    Represents the state of the graph workflow.

    Attributes:
        query (str): The user query.
        generation (str): The generated response from the system.
        documents (List[str]): The list of retrieved documents.
        document_sources (List[str]): The sources of the retrieved documents.
        max_retries_q (int): Maximum number of retries for query operations.
        max_retries_gen (int): Maximum number of retries for generation operations.
        msg (str): Additional messages or status updates during workflow execution.
    """
    query: str
    generation: str
    documents: List[str]
    document_sources: List[str]
    max_retries_q: int
    max_retries_gen: int
    msg: str

def create_graph_workflow():
    """
    Creates and compiles a graph-based workflow for handling user queries.

    The workflow consists of various nodes that handle operations such as retrieving
    documents, grading relevance, generating responses, and rewriting queries. Conditional
    logic determines the next steps in the workflow based on results from each node.

    Nodes in the workflow:
        - set_initial_vars: Initializes variables in the graph state.
        - retrieve: Retrieves relevant documents for the query.
        - grade_document: Grades the relevance of retrieved documents.
        - generate: Generates a response based on the query and retrieved documents.
        - rewrite_query: Rewrites the query to improve document retrieval.
        - grade_generation: Grades the usefulness of the generated response.

    Workflow edges:
        - Connects nodes sequentially or conditionally based on evaluation results.
        - Supports retry mechanisms and termination conditions.

    Returns:
        Callable: The compiled graph-based workflow application.
    """

    # Set the initial GraphState
    workflow = StateGraph(GraphState)

    # Define the nodes
    workflow.add_node("set_initial_vars", set_initial_vars)
    workflow.add_node("retrieve", retrieve)
    workflow.add_node("grade_document", grade_document)
    workflow.add_node("generate", generate)
    workflow.add_node("rewrite_query", rewrite_query)
    # workflow.add_node("query_decomposition", query_decomposition)

    # Build the Graph
    workflow.add_edge(START, "set_initial_vars")
    workflow.add_edge("set_initial_vars", "retrieve")
    workflow.add_edge("retrieve", "grade_document")
    workflow.add_conditional_edges(
        "grade_document", 
        decide_to_generate,
        {
            "generate": "generate",
            "rewrite_query": "rewrite_query",  
            "end": END     
        }
    )
    # workflow.add_edge("query_decomposition", "retrieve")
    workflow.add_edge("rewrite_query", "retrieve")
    workflow.add_conditional_edges(
        "generate", 
        grade_generation, 
        {
            "not supported": "generate",
            "useful": END,
            "not useful": "generate",
            "end": END
        }
    )

    # Compile the workflow
    app = workflow.compile()

    return app

