# Imports
from typing_extensions import TypedDict
from langgraph.graph import E<PERSON>, StateGraph, START
from nodes import * 

# Define the Graph state
class GraphState(TypedDict):
    csv_file_path: str
    output_file_path: str
    csv_file_contents: str
    pos_csv_file_path: str
    neg_csv_file_path: str
    edge_csv_file_path: str
    pos_fb: str
    neg_fb: str
    edge_fb: str
    pos_test_cases_fb: str
    neg_test_cases_fb: str
    edge_test_cases_fb: str

def create_graph_workflow():
    workflow = StateGraph(GraphState)

    # Define the nodes
    workflow.add_node("load_csv", load_csv)
    workflow.add_node("generate_pos_test_cases", generate_pos_test_cases)
    workflow.add_node("generate_neg_test_cases", generate_neg_test_cases)
    workflow.add_node("generate_edge_test_cases", generate_edge_test_cases)
    workflow.add_node("get_pos_feedback", get_pos_feedback)
    workflow.add_node("get_neg_feedback", get_neg_feedback)
    workflow.add_node("get_edge_feedback", get_edge_feedback)
    workflow.add_node("correct_pos_tests", correct_pos_tests)
    workflow.add_node("correct_neg_tests", correct_neg_tests)
    workflow.add_node("correct_edge_tests", correct_edge_tests)
    workflow.add_node("combine_csv_files", combine_csv_files)

    # Build the graph
    workflow.add_edge(START, "load_csv")
    workflow.add_edge("load_csv", "generate_pos_test_cases")
    workflow.add_edge("load_csv", "generate_neg_test_cases")
    workflow.add_edge("load_csv", "generate_edge_test_cases")
    workflow.add_edge("generate_pos_test_cases", "get_pos_feedback")
    workflow.add_edge("generate_neg_test_cases", "get_neg_feedback")
    workflow.add_edge("generate_edge_test_cases", "get_edge_feedback")
    workflow.add_edge("get_pos_feedback", "correct_pos_tests")
    workflow.add_edge("get_neg_feedback", "correct_neg_tests")
    workflow.add_edge("get_edge_feedback", "correct_edge_tests")
    workflow.add_edge('correct_pos_tests', 'combine_csv_files')
    workflow.add_edge('correct_neg_tests', 'combine_csv_files')
    workflow.add_edge('correct_edge_tests', 'combine_csv_files')
    workflow.add_edge('combine_csv_files', END)

    app = workflow.compile()

    return app

# -------------------------------------Graph for Update System------------------------------------ #
class UpdateGraphState(TypedDict):
    old_csv_file_path: str
    new_csv_file_path: str
    old_test_cases_file_path: str
    output_file_path: str
    type_gen: str
    rem_filtered_path: str
    new_test_path: str
    update_test_path: str
    removed_sections: List[Dict]
    new_sections: List[Dict]
    changed_rows: List[Dict]

def create_update_graph_workflow():
    update_workflow = StateGraph(UpdateGraphState)

    # Define the nodes
    update_workflow.add_node("check_differences", check_differences)
    update_workflow.add_node("remove_test_cases", remove_test_cases)
    update_workflow.add_node("new_test_cases", new_test_cases)
    update_workflow.add_node("update_test_cases", update_test_cases)
    update_workflow.add_node("finalize_csv", finalize_csv)

    # Build the graph
    update_workflow.add_edge(START, 'check_differences')
    update_workflow.add_edge('check_differences', 'remove_test_cases')
    update_workflow.add_edge('check_differences', 'new_test_cases')
    update_workflow.add_edge('check_differences', 'update_test_cases')
    update_workflow.add_edge('remove_test_cases', 'finalize_csv')
    update_workflow.add_edge('new_test_cases', 'finalize_csv')
    update_workflow.add_edge('update_test_cases', 'finalize_csv')

    update_app = update_workflow.compile()
    return update_app