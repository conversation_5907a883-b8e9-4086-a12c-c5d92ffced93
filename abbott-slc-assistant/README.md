# Test Case Generation Agent System

## Overview

This project leverages <PERSON><PERSON> to create an intelligent agent system that generates and refines test cases from a requirements CSV file. The system produces positive, negative, and edge test cases, and includes an automated feedback mechanism powered by another LLM to refine these test cases iteratively. Structured outputs are managed using Pydantic. Additionally, the project includes an update test cases agent system that handles changes in requirements, ensuring test cases remain up-to-date.

## Features

- **Test Case Generation**: Automatically generates and refines positive, negative, and edge test cases from a requirements CSV file.
- **Automated Feedback Mechanism**: Uses another LLM to provide feedback and refine test cases to improve accuracy and coverage.
- **Structured Outputs**: Utilizes Pydantic for structured and validated outputs.
- **Update Test Cases**: Handles updates to requirements, including additions, deletions, and modifications, ensuring test cases are always current.

## Installation

1. Clone the repository:
    ```bash
    git clone https://github.com/maungthura-abbott/abbott-slc-virtual-assistant.git
    cd abbott-slc-test-case-generator
    ```

2. Install the required dependencies:
    ```bash
    pip install -r requirements.txt
    ```

## Usage

### Running the Application

1. Run the Flask application:
    ```bash
    python app.py
    ```
2. Open your web browser and navigate to `http://localhost:5000`.

### Generating and Refining Test Cases

1. Navigate to the "Generate Test Cases" section.
2. Upload your requirements CSV file.
3. The system will generate and refine test cases, providing the output in the specified format (csv file).

### Updating Test Cases

1. Navigate to the "Update Test Cases" section.
2. Upload the old and new requirements CSV files, along with the old test cases file.
3. The system will handle the updates and provide the new test cases in a csv file.
