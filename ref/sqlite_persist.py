import sqlite3

class SQLitePersist:
    def __init__(self):
        self.db_name = 'gen_history.db'

        # Connect to SQLite database (or create it if it doesn't exist)
        conn = sqlite3.connect(self.db_name)

        try:
            # Create a cursor object to execute SQL commands
            cursor = conn.cursor()

            # Create a new table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS gen_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                llm_response TEXT,
                output_csv_path TEXT
            )
            ''')

            # Commit changes and close the connection
            conn.commit()
        finally:
            conn.close()

    def insert_data(self, file_name:str, file_path:str, llm_response:str, output_csv_path:str):
        """
        Inserts a new record into the gen_history table.
        
        :param file_name: Name of the file
        :param file_path: Path of the file
        :param llm_response: Response from the LLM
        :param output_csv_path: Path to the output CSV file
        """
        conn = sqlite3.connect(self.db_name)
        try:
            cursor = conn.cursor()
            
            cursor.execute('''
            INSERT INTO gen_history (file_name, file_path, llm_response, output_csv_path)
            VALUES (?, ?, ?, ?)
            ''', (file_name, file_path, llm_response, output_csv_path))
            conn.commit()
        finally:
            conn.close()

    def query_data(self, file_name:str=None, file_path:str=None) -> list:
        """
        Queries records from the gen_history table based on file name and/or file path.
        
        :param file_name: (Optional) Name of the file to filter by
        :param file_path: (Optional) Path of the file to filter by
        :return: List of matching records
        """
        conn = sqlite3.connect(self.db_name)
        try:
            cursor = conn.cursor()
            
            query = 'SELECT file_name, file_path, llm_response, output_csv_path FROM gen_history'
            params = []
            
            if file_name:
                query += ' WHERE file_name = ?'
                params.append(file_name)
            
            if file_path:
                if file_name:
                    query += ' AND'
                else:
                    query += ' WHERE'
                query += ' file_path = ?'
                params.append(file_path)
            
            cursor.execute(query, params)
            results = cursor.fetchall()
        finally:
            conn.close()
        
        return results
    
    def delete_data(self, file_name:str=None, file_path:str=None):
        """
        Deletes records from the gen_history table based on file name and/or file path.
        
        :param file_name: (Optional) Name of the file to filter by
        :param file_path: (Optional) Path of the file to filter by
        """
        conn = sqlite3.connect(self.db_name)
        try:
            cursor = conn.cursor()
            
            query = 'DELETE FROM gen_history'
            params = []
            
            if file_name:
                query += ' WHERE file_name = ?'
                params.append(file_name)
            
            if file_path:
                if file_name:
                    query += ' AND'
                else:
                    query += ' WHERE'
                query += ' file_path = ?'
                params.append(file_path)
            
            cursor.execute(query, params)
            conn.commit()
        finally:
            conn.close()
        
    def update_llm_response(self, new_llm_response:str, file_name:str=None, file_path:str=None):
        """
        Updates the llm_response field for records in the gen_history table based on file name and/or file path.
        
        :param new_llm_response: New response from the LLM
        :param file_name: (Optional) Name of the file to filter by
        :param file_path: (Optional) Path of the file to filter by
        """
        if not new_llm_response:
            raise ValueError('new_llm_response cannot be null or empty.')

        conn = sqlite3.connect(self.db_name)
        try:
            cursor = conn.cursor()
            
            query = 'UPDATE gen_history SET llm_response = ?'
            params = [new_llm_response]
            
            if file_name:
                query += ' WHERE file_name = ?'
                params.append(file_name)
            
            if file_path:
                if file_name:
                    query += ' AND'
                else:
                    query += ' WHERE'
                query += ' file_path = ?'
                params.append(file_path)
            
            cursor.execute(query, params)
            conn.commit()
        finally:
            conn.close()

    def count_records_by_file_name(self, file_name:str) -> int:
        """
        Counts the number of records in the gen_history table that match the given file name.
        
        :param file_name: Name of the file to filter by
        :return: Count of matching records
        """
        conn = sqlite3.connect(self.db_name)
        try:
            cursor = conn.cursor()
            
            query = 'SELECT COUNT(id) FROM gen_history WHERE file_name = ?'
            cursor.execute(query, (file_name,))
            
            count = cursor.fetchone()[0]
        finally:
            conn.close()
        return count
        
# Example usages:
slp = SQLitePersist()

# Insert a new record
slp.insert_data('example.txt', '/path/to/example.txt', 'Sample LLM response', '/path/to/output.csv')

# Query records by file name
print(slp.query_data(file_name='example.txt'))

# Query records by file path
print(slp.query_data(file_path='/path/to/example.txt'))

# Query records by both file name and file path
print(slp.query_data(file_name='example.txt', file_path='/path/to/example.txt'))

record_count = slp.count_records_by_file_name('example.txt')
assert(record_count==1)

# Update the LLM response by file name
slp.update_llm_response('Updated LLM response', file_name='example.txt')

# Update the LLM response by both file name and file path
slp.update_llm_response('Another updated response', file_name='example.txt', file_path='/path/to/example.txt')

# Delete a record by file name
slp.delete_data(file_name='example.txt')

# Delete a record by file path
slp.delete_data(file_path='/path/to/example.txt')