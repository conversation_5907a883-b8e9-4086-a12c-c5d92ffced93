# Adaptive Retrieval-Augmented Generation (RAG) Agent System

## Overview

This project leverages <PERSON><PERSON> to create an adaptive Retrieval-Augmented Generation (RAG) agent system. The system uses both keyword search (BM25) and vector search to retrieve documents. It implements ideas from the Contextual Retrieval paper to improve the initial retrieval of chunks. After retrieving the chunks, an agent checks their relevance to the user query. If fewer than two relevant chunks are found, another agent rewrites the user query for better retrieval, and the process is run again. The system then performs RAG to generate the answer, which is checked by an agent for hallucinations and relevance. The final output includes the generated answer and the documents from which the information was retrieved.

## Features

- **Adaptive Retrieval**: Combines keyword search (BM25) and vector search for document retrieval.
- **Contextual Retrieval**: Implements Contextual Retrieval techniques to improve initial chunk retrieval.
- **Relevance Checking**: An agent checks the relevance of retrieved chunks to the user query.
- **Query Rewriting**: If fewer than two relevant chunks are found, an agent rewrites the user query for better retrieval.
- **RAG Answer Generation**: Generates answers using Retrieval-Augmented Generation.
- **Hallucination and Relevance Check**: An agent checks the generated answer for hallucinations and relevance.
- **Document References**: Provides the user with the generated answer and the documents used for retrieval.

## Contextual Retrieval

The system uses ideas from the Contextual Retrieval paper to enhance the retrieval process. 

## Installation

1. Clone the repository:
    ```bash
    git clone https://github.com/maungthura-abbott/abbott-slc-virtual-assistant.git
    cd abbott-slc-assistant
    ```

2. Install the required dependencies:
    ```bash
    pip install -r requirements.txt
    ```

## Usage

### Running the Application

1. Run the Flask application:
    ```bash
    python app.py
    ```
2. Open your web browser and navigate to `http://localhost:5000`.

### Generating Answers

1. Navigate to the "Generate Answer" section.
2. Enter your query.
3. The system will retrieve relevant chunks, generate an answer, and provide the documents used for retrieval.
