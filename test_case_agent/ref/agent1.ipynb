{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# Imports\n", "import json \n", "from langchain_core.prompts import ChatPromptTemplate, PromptTemplate\n", "from pydantic import BaseModel, Field\n", "from typing import List, Dict\n", "from langchain_openai import AzureChatOpenAI\n", "from langchain_core.output_parsers import StrOutputParser\n", "from typing_extensions import TypedDict\n", "from IPython.display import Image\n", "from pprint import pprint\n", "import os\n", "from langchain_community.document_loaders.csv_loader import CSVLoader\n", "from dotenv import load_dotenv\n", "import pandas as pd\n", "import csv\n", "import re\n", "import io\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# # Load the csv using langchain\n", "# file_path = \"./requirements.csv\"\n", "\n", "# loader = CSVLoader(file_path=file_path)\n", "# data = loader.load()\n", "\n", "# # view the first 5 rows\n", "# # for d in data[:5]:\n", "# #     print(d)\n", "# data[0].page_content"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Req ID: 480\n", "Name: US Page Refresh\n", "Description: Given: I have filled out the form fields and/or drop downs of either sensor replacement request form. When: I refresh the page. Then: the information I added remains in the fields, including any error states\n", "\n", "Req ID: 538\n", "Name: US - Render Sensor Replacement Eligibility Page\n", "Description: Given: User is on Guided Support Experience. When: User is navigated to Sensor Replacement Request from the Error message flow. Then: System will render eligibility page with following options:. . Checkbox to acknowledge privacy agreement. Submit button. . Rules list:. R1: User must agree to privacy acknowledgement prior to submit button becoming enabled. R2: If user clicks link at the end of the paragraph on sensitive data collection and usage, then they are directed to Abbott Privacy Resources\n", "\n", "Req ID: 594\n", "Name: US Google Address Validation\n", "Description: Given: The User is on the Your Information page on the Sensor Fell Off/Error message forms. When: The User starts typing in the Street Address Line 1 field. Then: The system should show address suggestions in a drop-down. And: When the User selects an option, the fields below are populated:. . Street address line 1. Street address line 2 (if applicable). City. State. Postal Code\n", "\n", "Req ID: 534\n", "Name: Sensor Serial Number - No Error\n", "Description: Given User is on the page of the sensor error OR sensor fell off replacement form. When User is populating the sensor serial number field. Then it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.\n", "\n", "Req ID: 535\n", "Name: Sensor Serial Number - Error\n", "Description: Given User on the page of the sensor error OR sensor fell off replacement form. When User is populating the sensor serial number field with invalid characters or length. Then there will be an error message displayed on the box, to Enter a valid sensor serial number or it does not allow to type beyond 11 characters.. . Rule 1:. 9, 10 and 11 characters are valid sensor number length, any other length should invoke the error message to enter a valid serial number.\n", "\n", "Req ID: 595\n", "Name: US Field Validation - Phone Number\n", "Description: Given: The User has accessed the Sensor Fell Off/Error message form page . When: The User interacts with the Phone Number field. Then: The system should show the validation and messaging including: . . Numeric values only. 10 digit max. Focus away from field without entering a 10 digit phone number displays error state per the design with an error message to enter 10-digit phone number including area code with no hyphens or spaces.\n", "\n", "Req ID: 539\n", "Name: US - Render Sensor Information Page\n", "Description: Given: User is on Guided Support Experience. When: User acknowledges and submits eligibility form. Then: System will render sensor information page with following fields:. . Sensor Serial Number. How many days were you wearing the sensor?. Is your sensor available for return to Abbott?. What is the message event code?. . Rules list:. R1. User must complete all required fields with valid entries prior to next button becoming enabled\n", "\n", "Req ID: 481\n", "Name: Changing sensor replacement request type\n", "Description: Given: I have filled out the form fields and/or drop downs of either sensor replacement request form. When: I change sensor replacement type (from error message to fell off or vice versa) and revisit the form. Then: the information is cleared\n", "\n", "Req ID: 482\n", "Name: US URL change within the same locale\n", "Description: Given I am on the Sensor fall off or Senor error message webform. When I change the URL to GSE guided questions of the same locale. And I navigate forward to the fall off or error message webform. Then my previously entered data will be retained in the relevant fields\n", "\n", "Req ID: 540\n", "Name: US - Render Your Information Page\n", "Description: Given: User is on Guided Support Experience. When: User completes and submits sensor information page. Then: System will render your information page with following fields:. . First Name. Last Name. Phone. Email. Street Address Line 1. Street Address Line 2. City. State. Postal Code. . Rules list:. R1. User must complete all required fields with valid entries prior to submit button becoming enabled\n", "\n", "Req ID: 596\n", "Name: US Field Validation - Postal code\n", "Description: Given: The User has accessed the Sensor Fell Off/Error message form page . When: The User interacts with the mandatory Postal code field. Then: The system should show the validation and messaging including: . . Limit input to 5 numeric characters including the space (cannot type beyond the 5th character). Validation message requesting to enter a valid postal code.\n", "\n", "Req ID: 600\n", "Name: Field Validation - First Name\n", "Description: Given: The User has accessed the sensor error OR sensor fell off replacement form page . When: The User interacts with the mandatory First Name field. Then: The system should show the validation and messaging including: . Allow any character to be entered within field. 40 character max. Focus away from field without entering anything displays error state per the design with message requesting user to enter their first name.\n", "\n", "Req ID: 601\n", "Name: Field Validation - Last Name\n", "Description: Given: The User has accessed the sensor error OR sensor fell off replacement form page . When: The User interacts with the mandatory Last Name field. Then: The system should show the validation and messaging including: . . Allow any character to be entered within field. 40 character max. Focus away from field without entering anything displays error state per the design with message requesting user to enter their last name.\n", "\n", "Req ID: 597\n", "Name: US Field Validation - State\n", "Description: Given: The User has accessed the Sensor Fell Off/Error message form page . When: The User interacts with the State field. Then: The system should show the validation and messaging including: . . Only accepts 2 alpha characters input. Must be one of the following two alpha character combinations:. . AL, AK, AZ, AR, CA, CO, CT, DE, DC, FL, GA, HI, ID, IL, IN, IA, KS, KY, LA, ME, MT, NE, NV, NH, NJ, NM, NY, NC, ND, OH, OK, OR, MD, MA, MI, MN, MS, MO, PA, RI, SC, SD, TN, TX, UT, VT, VA, WA, WV, WI, WY, MP, VI, PR, . MH, AA, AE, AP, GU, AS, FM, PW, UM. . Validation error message should request to enter state’s 2-character abbreviation.\n", "\n", "Req ID: 541\n", "Name: US - Successful Submission\n", "Description: Given: User is on Guided Support Experience. When: User completes and submits your information page. Then: System will render a confirmation page with submission confirmation and information on what happens next including processing time.. . Rules list:. R1. clicking on the Customer Support phone number (************) will invoke the browser call functionality\n", "\n", "Req ID: 483\n", "Name: US URL change to a different locale\n", "Description: Given I am on the Sensor fall off or Senor error message webform. When I change the URL to GSE guided questions of a different locale. Then I should see the locale change pop up modal. And my data should not be persisted from the prior locale when I proceed to the new locale\n", "\n", "Req ID: 576\n", "Name: US PWD opts to start online replacement request\n", "Description: Given: User is on the Eligibility page. When: User accepts the privacy agreement and clicks Submit. Then: System will direct user to sensor error replacement request form\n", "\n", "Req ID: 598\n", "Name: US Address Validation - Returns a new selection\n", "Description: Given: User has completed all required address fields on the Sensor Fell Off/Error message forms and they are not an exact match with the address validation service . When: User clicks submit on the form. Then: a pop-up modal with a suggested address is displayed for the user to either select or continue with the address currently in the fields, as well as an Edit link, and a disabled submit button. . Rules list:. R1. If user makes a selection and clicks submit,&nbsp; the submit button becomes enabled and the form can be submitted with the selected address. R2. If User clicks on edit button, then user is returned to the form\n", "\n", "Req ID: 602\n", "Name: Field Validation - Email\n", "Description: Given: The User has accessed the sensor error OR sensor fell off replacement form page . When: The User interacts with the mandatory email field. Then: The system should show the validation and messaging including: . . A valid format of email. Focus away from field without entering a valid email displays error state per the design with message requesting user to enter a valid email address.\n", "\n", "Req ID: 603\n", "Name: <PERSON> - How many days were you wearing the sensor?\n", "Description: Given: The User has accessed the sensor error OR sensor fell off replacement form page . When: The User interacts with the mandatory How many days were you wearing the sensor? field. Then: The system should show the options (based on the product chosen): . . I'm not sure. Same day. 1 day. 2 days. 3 days. …. 14 days. 15 days (Applicable for FSL 2 Plus and FSL 3 Plus only)\n", "\n", "Req ID: 599\n", "Name: US Address Validation - Cannot find a matching address\n", "Description: Given: The User has completed all required address fields on Sensor Fell Off/Error message form and the address validation service does not have a matching address. When: The User submits the form. Then: a pop-up with no suggested address is displayed to either continue with the address as written or make changes to their input through an Edit link. . Rules list:. &nbsp;&nbsp;&nbsp; R1. If user opts to continue with entered address and clicks submit,&nbsp;the form is submitted with selected address. &nbsp;&nbsp;&nbsp; R2. If User clicks on edit button, then user is returned to the form\n", "\n", "Req ID: 604\n", "Name: Field - Is your sensor available for return to Abbott, if requested?\n", "Description: Given: The User has accessed the sensor error OR sensor fell off replacement form page . When: The User interacts with the mandatory Is your sensor available for return to Abbott, if requested? drop down field. Then: The system should show the below options: . . Yes. No\n", "\n", "\n"]}], "source": ["def get_csv_contents(file_path):\n", "    csv_file_contents = \"\"\n", "    with open(file_path, \"r\", encoding=\"utf-8\", errors=\"replace\") as file:\n", "        reader = csv.reader(file)\n", "        next(reader)  # Skip the header row\n", "        for row in reader:\n", "            csv_file_contents += f\"Req ID: {row[0]}\\n\"\n", "            csv_file_contents += f\"Name: {row[1]}\\n\"\n", "            desc = row[2].replace('\\\\n', '\\n').replace(\"\\n\", \". \").replace(\"\\r\", \"\")\n", "            csv_file_contents += f\"Description: {desc}\"\n", "            csv_file_contents += \"\\n\\n\"\n", "\n", "    return csv_file_contents\n", "csv_file_contents = get_csv_contents(file_path='requirements.csv')\n", "print(csv_file_contents)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# csv_file_contents = \"\"\n", "# for d in data:\n", "#     csv_file_contents += d.page_content + \"\\n\\n\"\n", "# print(csv_file_contents)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Chains"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["TestCases(test_cases=[TestCase(requirement_id='480', test_case_description='Verify that the form data remains after refreshing the page.', inputs='Fill out the form fields and/or drop downs of the sensor replacement request form, then refresh the page.', expected_outcome='The information added remains in the fields, including any error states.'), TestCase(requirement_id='538', test_case_description='Verify that the eligibility page is rendered with the correct options and rules.', inputs='Navigate to Sensor Replacement Request from the Error message flow.', expected_outcome='The eligibility page is rendered with a checkbox to acknowledge privacy agreement, a submit button, and the rules are enforced (privacy agreement must be acknowledged before submit button is enabled, and clicking the link directs to Abbott Privacy Resources).'), TestCase(requirement_id='594', test_case_description='Verify that address suggestions are shown and fields are populated correctly.', inputs='Start typing in the Street Address Line 1 field on the Your Information page.', expected_outcome='Address suggestions are shown in a drop-down, and selecting an option populates Street address line 1, Street address line 2 (if applicable), City, State, and Postal Code fields.'), TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field accepts valid alphanumeric characters and lengths.', inputs=\"Enter a valid sensor serial number (9, 10, or 11 characters long, excluding 'B', 'I', 'O', and 'S').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.'), TestCase(requirement_id='595', test_case_description='Verify that the phone number field validates input correctly.', inputs='Enter a 10-digit phone number without hyphens or spaces in the Phone Number field and focus away from the field.', expected_outcome='The phone number is accepted without error.'), TestCase(requirement_id='539', test_case_description='Verify that the sensor information page is rendered with the correct fields and rules.', inputs='Acknowledge and submit the eligibility form.', expected_outcome='The sensor information page is rendered with fields for Sensor Serial Number, How many days were you wearing the sensor?, Is your sensor available for return to Abbott?, and What is the message event code?. All required fields must be completed with valid entries before the next button is enabled.'), TestCase(requirement_id='482', test_case_description='Verify that previously entered data is retained when changing the URL within the same locale.', inputs='Enter data in the Sensor fall off or Sensor error message webform, change the URL to GSE guided questions of the same locale, and navigate forward to the fall off or error message webform.', expected_outcome='Previously entered data is retained in the relevant fields.'), TestCase(requirement_id='540', test_case_description='Verify that the Your Information page is rendered with the correct fields and rules.', inputs='Complete and submit the sensor information page.', expected_outcome='The Your Information page is rendered with fields for First Name, Last Name, Phone, Email, Street Address Line 1, Street Address Line 2, City, State, and Postal Code. All required fields must be completed with valid entries before the submit button is enabled.'), TestCase(requirement_id='596', test_case_description='Verify that the postal code field validates input correctly.', inputs='Enter a 5-digit postal code in the Postal code field.', expected_outcome='The postal code is accepted without error.'), TestCase(requirement_id='600', test_case_description='Verify that the first name field validates input correctly.', inputs='Enter a valid first name (up to 40 characters) in the First Name field and focus away from the field.', expected_outcome='The first name is accepted without error.'), TestCase(requirement_id='601', test_case_description='Verify that the last name field validates input correctly.', inputs='Enter a valid last name (up to 40 characters) in the Last Name field and focus away from the field.', expected_outcome='The last name is accepted without error.'), TestCase(requirement_id='597', test_case_description='Verify that the state field validates input correctly.', inputs='Enter a valid 2-character state abbreviation in the State field.', expected_outcome='The state abbreviation is accepted without error.'), TestCase(requirement_id='541', test_case_description='Verify that the confirmation page is rendered after successful submission.', inputs='Complete and submit the Your Information page.', expected_outcome='The confirmation page is rendered with submission confirmation and information on what happens next, including processing time. Clicking on the Customer Support phone number invokes the browser call functionality.'), TestCase(requirement_id='576', test_case_description='Verify that the user is directed to the sensor error replacement request form after accepting the privacy agreement and clicking Submit.', inputs='Accept the privacy agreement and click Submit on the Eligibility page.', expected_outcome='The user is directed to the sensor error replacement request form.'), TestCase(requirement_id='598', test_case_description='Verify that a pop-up modal with a suggested address is displayed when the address validation service returns a new selection.', inputs='Complete all required address fields with an address that is not an exact match with the address validation service and click submit.', expected_outcome='A pop-up modal with a suggested address is displayed for the user to either select or continue with the address currently in the fields, as well as an Edit link, and a disabled submit button. If the user makes a selection and clicks submit, the submit button becomes enabled and the form can be submitted with the selected address. If the user clicks on the edit button, they are returned to the form.'), TestCase(requirement_id='602', test_case_description='Verify that the email field validates input correctly.', inputs='Enter a valid email address in the Email field and focus away from the field.', expected_outcome='The email address is accepted without error.'), TestCase(requirement_id='603', test_case_description=\"Verify that the 'How many days were you wearing the sensor?' field shows the correct options.\", inputs=\"Interact with the 'How many days were you wearing the sensor?' field.\", expected_outcome=\"The field shows the options: I'm not sure, Same day, 1 day, 2 days, 3 days, …, 14 days, 15 days (Applicable for FSL 2 Plus and FSL 3 Plus only).\"), TestCase(requirement_id='604', test_case_description=\"Verify that the 'Is your sensor available for return to Abbott, if requested?' field shows the correct options.\", inputs=\"Interact with the 'Is your sensor available for return to Abbott, if requested?' drop down field.\", expected_outcome='The field shows the options: Yes, No.')])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["### Positive test case chain\n", "# Pydantic for structured output\n", "class TestCase(BaseModel):\n", "    requirement_id: str = Field(description=\"Requirement Id from the csv\")\n", "    # requirement_description: str = Field(description=\"Requirement text from the csv\")\n", "    test_case_description: str = Field(description=\"Test case that validates the requirement\")\n", "    inputs: str = Field(description=\"List of inputs for the test case\")\n", "    expected_outcome: str = Field(description=\"Expected result of the test case\")\n", "\n", "class TestCases(BaseModel):\n", "    test_cases: List[TestCase] = Field(description=\"A list of the test cases\")\n", "\n", "\n", "pos_tst_prompt_template = \"\"\"\n", "You are an LLM agent responsible for generating positive test cases for a project based on the requirements provided in a CSV file. The CSV file contains each requirement in a structured format. Your task is to analyze each requirement and produce positive test cases only where applicable, confirming the correct and expected functionality of the project for each relevant requirement.\n", "\n", "Follow these guidelines:\n", "1. Carefully read each requirement from the CSV file.\n", "2. Generate one or more positive test cases per requirement if possible. Positive test cases should validate that the feature or function works as intended and meets the requirement.\n", "3. Each test case should be detailed, specifying inputs, expected outcomes, and any relevant setup.\n", "4. Skip any requirements for which a positive test case is not applicable. Avoid generating negative test cases or edge cases, as these are out of scope for this task.\n", "\n", "Format each test case in this structure:\n", "- Requirement ID: [ID from CSV]\n", "- Test Case Description: [Test case that validates the requirement]\n", "- Inputs: [List of inputs for the test case]\n", "- Expected Outcome: [Expected result of the test case]\n", "\n", "Example:\n", "Requirement ID: 001\n", "Test Case Description: \"Verify successful login with correct username and password.\"\n", "Inputs: {{\"username\": \"validUser\", \"password\": \"validPass123\"}}\n", "Expected Outcome: \"User is successfully logged in and redirected to the dashboard.\"\n", "\n", "Generate a complete list of positive test cases only for requirements that allow for positive test cases.\n", "Here is the CSV file contents: \n", "{csv_file}\n", "\"\"\"\n", "pos_tst_prompt = PromptTemplate(input_variables=['csv_file'], template=pos_tst_prompt_template)\n", "\n", "# Initialize the llm\n", "llm = AzureChatOpenAI(model='gpt-4o', temperature=0)\n", "structured_llm = llm.with_structured_output(TestCases)\n", "pos_generator = pos_tst_prompt | structured_llm\n", "\n", "# Test it out\n", "res = pos_generator.invoke({\"csv_file\": csv_file_contents})\n", "res"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[TestCase(requirement_id='480', test_case_description='Verify that the form data remains after refreshing the page.', inputs='Fill out the form fields and/or drop downs of the sensor replacement request form, then refresh the page.', expected_outcome='The information added remains in the fields, including any error states.'),\n", " TestCase(requirement_id='538', test_case_description='Verify that the eligibility page is rendered with the correct options and rules.', inputs='Navigate to Sensor Replacement Request from the Error message flow.', expected_outcome='The eligibility page is rendered with a checkbox to acknowledge privacy agreement, a submit button, and the rules are enforced (privacy agreement must be acknowledged before submit button is enabled, and clicking the link directs to Abbott Privacy Resources).'),\n", " TestCase(requirement_id='594', test_case_description='Verify that address suggestions are shown and fields are populated correctly.', inputs='Start typing in the Street Address Line 1 field on the Your Information page.', expected_outcome='Address suggestions are shown in a drop-down, and selecting an option populates Street address line 1, Street address line 2 (if applicable), City, State, and Postal Code fields.'),\n", " TestCase(requirement_id='534', test_case_description='Verify that the sensor serial number field accepts valid alphanumeric characters and lengths.', inputs=\"Enter a valid sensor serial number (9, 10, or 11 characters long, excluding 'B', 'I', 'O', and 'S').\", expected_outcome='The sensor serial number is accepted and displayed in uppercase.'),\n", " TestCase(requirement_id='595', test_case_description='Verify that the phone number field validates input correctly.', inputs='Enter a 10-digit phone number without hyphens or spaces in the Phone Number field and focus away from the field.', expected_outcome='The phone number is accepted without error.'),\n", " TestCase(requirement_id='539', test_case_description='Verify that the sensor information page is rendered with the correct fields and rules.', inputs='Acknowledge and submit the eligibility form.', expected_outcome='The sensor information page is rendered with fields for Sensor Serial Number, How many days were you wearing the sensor?, Is your sensor available for return to <PERSON>?, and What is the message event code?. All required fields must be completed with valid entries before the next button is enabled.'),\n", " TestCase(requirement_id='482', test_case_description='Verify that previously entered data is retained when changing the URL within the same locale.', inputs='Enter data in the Sensor fall off or Sensor error message webform, change the URL to GSE guided questions of the same locale, and navigate forward to the fall off or error message webform.', expected_outcome='Previously entered data is retained in the relevant fields.'),\n", " TestCase(requirement_id='540', test_case_description='Verify that the Your Information page is rendered with the correct fields and rules.', inputs='Complete and submit the sensor information page.', expected_outcome='The Your Information page is rendered with fields for First Name, Last Name, Phone, Email, Street Address Line 1, Street Address Line 2, City, State, and Postal Code. All required fields must be completed with valid entries before the submit button is enabled.'),\n", " TestCase(requirement_id='596', test_case_description='Verify that the postal code field validates input correctly.', inputs='Enter a 5-digit postal code in the Postal code field.', expected_outcome='The postal code is accepted without error.'),\n", " TestCase(requirement_id='600', test_case_description='Verify that the first name field validates input correctly.', inputs='Enter a valid first name (up to 40 characters) in the First Name field and focus away from the field.', expected_outcome='The first name is accepted without error.'),\n", " TestCase(requirement_id='601', test_case_description='Verify that the last name field validates input correctly.', inputs='Enter a valid last name (up to 40 characters) in the Last Name field and focus away from the field.', expected_outcome='The last name is accepted without error.'),\n", " TestCase(requirement_id='597', test_case_description='Verify that the state field validates input correctly.', inputs='Enter a valid 2-character state abbreviation in the State field.', expected_outcome='The state abbreviation is accepted without error.'),\n", " TestCase(requirement_id='541', test_case_description='Verify that the confirmation page is rendered after successful submission.', inputs='Complete and submit the Your Information page.', expected_outcome='The confirmation page is rendered with submission confirmation and information on what happens next, including processing time. Clicking on the Customer Support phone number invokes the browser call functionality.'),\n", " TestCase(requirement_id='576', test_case_description='Verify that the user is directed to the sensor error replacement request form after accepting the privacy agreement and clicking Submit.', inputs='Accept the privacy agreement and click Submit on the Eligibility page.', expected_outcome='The user is directed to the sensor error replacement request form.'),\n", " TestCase(requirement_id='598', test_case_description='Verify that a pop-up modal with a suggested address is displayed when the address validation service returns a new selection.', inputs='Complete all required address fields with an address that is not an exact match with the address validation service and click submit.', expected_outcome='A pop-up modal with a suggested address is displayed for the user to either select or continue with the address currently in the fields, as well as an Edit link, and a disabled submit button. If the user makes a selection and clicks submit, the submit button becomes enabled and the form can be submitted with the selected address. If the user clicks on the edit button, they are returned to the form.'),\n", " TestCase(requirement_id='602', test_case_description='Verify that the email field validates input correctly.', inputs='Enter a valid email address in the Email field and focus away from the field.', expected_outcome='The email address is accepted without error.'),\n", " TestCase(requirement_id='603', test_case_description=\"Verify that the 'How many days were you wearing the sensor?' field shows the correct options.\", inputs=\"Interact with the 'How many days were you wearing the sensor?' field.\", expected_outcome=\"The field shows the options: I'm not sure, Same day, 1 day, 2 days, 3 days, …, 14 days, 15 days (Applicable for FSL 2 Plus and FSL 3 Plus only).\"),\n", " TestCase(requirement_id='604', test_case_description=\"Verify that the 'Is your sensor available for return to Abbott, if requested?' field shows the correct options.\", inputs=\"Interact with the 'Is your sensor available for return to Abbott, if requested?' drop down field.\", expected_outcome='The field shows the options: Yes, No.')]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["res.test_cases"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["18"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(res.test_cases)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV file './test_cases/positive_test_cases.csv' has been created successfully.\n"]}], "source": ["def generate_test_case_csv(test_cases, prefix, output_file='test_cases.csv', req_desc_file='requirements.csv'):\n", "    # Verify that prefix is one of the allowed values\n", "    if prefix not in ['p', 'n', 'e']:\n", "        raise ValueError(\"Prefix must be 'p', 'n', or 'e'.\")\n", "\n", "    # Load requirement descriptions from the additional CSV file using pandas\n", "    req_descriptions_df = pd.read_csv(req_desc_file)\n", "    # Rename columns for consistency, if necessary\n", "    req_descriptions_df.columns = ['requirement_id', 'Name', 'requirement_description']\n", "\n", "    # Convert test cases to a pandas DataFrame\n", "    test_case_data = []\n", "    for idx, test_case in enumerate(test_cases):\n", "        test_case_id = f\"{prefix}{test_case.requirement_id}_{idx + 1}\"\n", "        test_case_data.append({\n", "            'test_case_id': test_case_id,\n", "            'requirement_id': int(test_case.requirement_id),\n", "            'test_case_description': test_case.test_case_description,\n", "            'inputs': test_case.inputs,\n", "            'expected_outcome': test_case.expected_outcome\n", "        })\n", "\n", "    test_cases_df = pd.DataFrame(test_case_data)\n", "\n", "    # Merge the test cases DataFrame with the requirement descriptions DataFrame on requirement_id\n", "    merged_df = pd.merge(test_cases_df, req_descriptions_df[['requirement_id', 'requirement_description']], \n", "                         on='requirement_id', how='left')\n", "\n", "    # Fill in any missing descriptions with a default message (avoid inplace=True to avoid the warning)\n", "    merged_df['requirement_description'] = merged_df['requirement_description'].fillna(\"No description available\")\n", "\n", "    # Save the merged DataFrame to CSV\n", "    merged_df.to_csv(output_file, index=False)\n", "    print(f\"CSV file '{output_file}' has been created successfully.\")\n", "\n", "generate_test_case_csv(test_cases=res.test_cases, prefix='p', output_file='./test_cases/positive_test_cases.csv')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["TestCases(test_cases=[TestCase(requirement_id='480', test_case_description='Verify that refreshing the page with invalid form data retains the invalid data and error states.', inputs='{ \"formData\": { \"field1\": \"invalidData\", \"field2\": \"\" }, \"action\": \"refreshPage\" }', expected_outcome='The form retains the invalid data and displays the appropriate error messages.'), TestCase(requirement_id='538', test_case_description='Verify that the submit button remains disabled if the user does not agree to the privacy acknowledgement.', inputs='{ \"privacyAcknowledgement\": false }', expected_outcome='The submit button remains disabled and the user cannot proceed.'), TestCase(requirement_id='594', test_case_description='Verify that entering an invalid address does not populate the address fields.', inputs='{ \"addressInput\": \"Invalid Address\" }', expected_outcome='The address fields remain empty and an error message is displayed.'), TestCase(requirement_id='534', test_case_description='Verify that entering invalid characters in the sensor serial number field displays an error message.', inputs='{ \"sensorSerialNumber\": \"12345B7890\" }', expected_outcome='An error message is displayed indicating invalid characters.'), TestCase(requirement_id='535', test_case_description='Verify that entering a sensor serial number with invalid length displays an error message.', inputs='{ \"sensorSerialNumber\": \"12345678\" }', expected_outcome='An error message is displayed indicating invalid length.'), TestCase(requirement_id='595', test_case_description='Verify that entering a phone number with non-numeric characters displays an error message.', inputs='{ \"phoneNumber\": \"************\" }', expected_outcome='An error message is displayed indicating invalid phone number format.'), TestCase(requirement_id='539', test_case_description='Verify that the next button remains disabled if required fields are not completed with valid entries.', inputs='{ \"requiredFields\": { \"sensorSerialNumber\": \"\", \"daysWorn\": \"\" } }', expected_outcome='The next button remains disabled and the user cannot proceed.'), TestCase(requirement_id='481', test_case_description='Verify that changing the sensor replacement type clears the form data.', inputs='{ \"formData\": { \"field1\": \"data1\", \"field2\": \"data2\" }, \"action\": \"changeReplacementType\" }', expected_outcome='The form data is cleared and the fields are empty.'), TestCase(requirement_id='482', test_case_description='Verify that changing the URL within the same locale retains the previously entered data.', inputs='{ \"url\": \"newURL\", \"formData\": { \"field1\": \"data1\", \"field2\": \"data2\" } }', expected_outcome='The previously entered data is retained in the relevant fields.'), TestCase(requirement_id='540', test_case_description='Verify that the submit button remains disabled if required fields are not completed with valid entries.', inputs='{ \"requiredFields\": { \"firstName\": \"\", \"lastName\": \"\" } }', expected_outcome='The submit button remains disabled and the user cannot proceed.'), TestCase(requirement_id='596', test_case_description='Verify that entering a postal code with more than 5 characters displays an error message.', inputs='{ \"postalCode\": \"123456\" }', expected_outcome='An error message is displayed indicating invalid postal code length.'), TestCase(requirement_id='600', test_case_description='Verify that leaving the first name field empty displays an error message.', inputs='{ \"firstName\": \"\" }', expected_outcome='An error message is displayed requesting the user to enter their first name.'), TestCase(requirement_id='601', test_case_description='Verify that leaving the last name field empty displays an error message.', inputs='{ \"lastName\": \"\" }', expected_outcome='An error message is displayed requesting the user to enter their last name.'), TestCase(requirement_id='597', test_case_description='Verify that entering an invalid state abbreviation displays an error message.', inputs='{ \"state\": \"XX\" }', expected_outcome='An error message is displayed indicating invalid state abbreviation.'), TestCase(requirement_id='602', test_case_description='Verify that entering an invalid email format displays an error message.', inputs='{ \"email\": \"invalidEmail\" }', expected_outcome='An error message is displayed requesting the user to enter a valid email address.'), TestCase(requirement_id='599', test_case_description='Verify that submitting the form with an address that cannot be matched displays a pop-up with no suggested address.', inputs='{ \"address\": \"123 Invalid St\" }', expected_outcome='A pop-up is displayed with no suggested address and options to continue or edit the address.'), TestCase(requirement_id='604', test_case_description='Verify that not selecting an option for the sensor return field displays an error message.', inputs='{ \"sensorReturn\": \"\" }', expected_outcome='An error message is displayed requesting the user to select an option for the sensor return field.')])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["### Negative test case chain\n", "neg_tst_prompt_template = \"\"\"\n", "You are an LLM agent responsible for generating negative test cases for a project based on the requirements provided in a CSV file. The CSV file contains each requirement in a structured format. Your task is to analyze each requirement and produce negative test cases only where applicable, confirming that the system handles errors, invalid inputs, and unexpected conditions appropriately for each relevant requirement.\n", "\n", "Follow these guidelines:\n", "1. Carefully read each requirement from the CSV file.\n", "2. Generate one or more negative test cases per requirement if possible. Negative test cases should validate that the feature or function appropriately handles invalid inputs, errors, or unexpected conditions, aligning with the requirement.\n", "3. Each test case should be detailed, specifying invalid inputs, expected outcomes, and any relevant setup.\n", "4. Skip any requirements for which a negative test case is not applicable. Avoid generating positive test cases, as these are out of scope for this task.\n", "\n", "Format each test case in this structure:\n", "- Requirement ID: [ID from CSV]\n", "- Test Case Description: [Test case that validates handling of invalid inputs or errors]\n", "- Inputs: [List of invalid inputs for the test case]\n", "- Expected Outcome: [Expected error message or result of the test case]\n", "\n", "Example:\n", "Requirement ID: 001\n", "Test Case Description: \"Verify login fails with incorrect username and password.\"\n", "Inputs: {{\"username\": \"invalidUser\", \"password\": \"wrongPass123\"}}\n", "Expected Outcome: \"User receives an error message indicating invalid login credentials.\"\n", "\n", "Generate a complete list of negative test cases only for requirements that allow for negative test cases.\n", "Here is the CSV file contents: \n", "{csv_file}\n", "\"\"\"\n", "\n", "neg_tst_prompt = PromptTemplate(input_variables=['csv_file'], template=neg_tst_prompt_template)\n", "\n", "neg_generator = neg_tst_prompt | structured_llm\n", "\n", "# Test it out\n", "neg_res = neg_generator.invoke({\"csv_file\": csv_file_contents})\n", "neg_res"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["[TestCase(requirement_id='480', test_case_description='Verify that refreshing the page with invalid form data retains the invalid data and error states.', inputs='{ \"formData\": { \"field1\": \"invalidData\", \"field2\": \"\" }, \"action\": \"refreshPage\" }', expected_outcome='The form retains the invalid data and displays the appropriate error messages.'),\n", " TestCase(requirement_id='538', test_case_description='Verify that the submit button remains disabled if the user does not agree to the privacy acknowledgement.', inputs='{ \"privacyAcknowledgement\": false }', expected_outcome='The submit button remains disabled and the user cannot proceed.'),\n", " TestCase(requirement_id='594', test_case_description='Verify that entering an invalid address does not populate the address fields.', inputs='{ \"addressInput\": \"Invalid Address\" }', expected_outcome='The address fields remain empty and an error message is displayed.'),\n", " TestCase(requirement_id='534', test_case_description='Verify that entering invalid characters in the sensor serial number field displays an error message.', inputs='{ \"sensorSerialNumber\": \"12345B7890\" }', expected_outcome='An error message is displayed indicating invalid characters.'),\n", " TestCase(requirement_id='535', test_case_description='Verify that entering a sensor serial number with invalid length displays an error message.', inputs='{ \"sensorSerialNumber\": \"12345678\" }', expected_outcome='An error message is displayed indicating invalid length.'),\n", " TestCase(requirement_id='595', test_case_description='Verify that entering a phone number with non-numeric characters displays an error message.', inputs='{ \"phoneNumber\": \"************\" }', expected_outcome='An error message is displayed indicating invalid phone number format.'),\n", " TestCase(requirement_id='539', test_case_description='Verify that the next button remains disabled if required fields are not completed with valid entries.', inputs='{ \"requiredFields\": { \"sensorSerialNumber\": \"\", \"daysWorn\": \"\" } }', expected_outcome='The next button remains disabled and the user cannot proceed.'),\n", " TestCase(requirement_id='481', test_case_description='Verify that changing the sensor replacement type clears the form data.', inputs='{ \"formData\": { \"field1\": \"data1\", \"field2\": \"data2\" }, \"action\": \"changeReplacementType\" }', expected_outcome='The form data is cleared and the fields are empty.'),\n", " TestCase(requirement_id='482', test_case_description='Verify that changing the URL within the same locale retains the previously entered data.', inputs='{ \"url\": \"newURL\", \"formData\": { \"field1\": \"data1\", \"field2\": \"data2\" } }', expected_outcome='The previously entered data is retained in the relevant fields.'),\n", " TestCase(requirement_id='540', test_case_description='Verify that the submit button remains disabled if required fields are not completed with valid entries.', inputs='{ \"requiredFields\": { \"firstName\": \"\", \"lastName\": \"\" } }', expected_outcome='The submit button remains disabled and the user cannot proceed.'),\n", " TestCase(requirement_id='596', test_case_description='Verify that entering a postal code with more than 5 characters displays an error message.', inputs='{ \"postalCode\": \"123456\" }', expected_outcome='An error message is displayed indicating invalid postal code length.'),\n", " TestCase(requirement_id='600', test_case_description='Verify that leaving the first name field empty displays an error message.', inputs='{ \"firstName\": \"\" }', expected_outcome='An error message is displayed requesting the user to enter their first name.'),\n", " TestCase(requirement_id='601', test_case_description='Verify that leaving the last name field empty displays an error message.', inputs='{ \"lastName\": \"\" }', expected_outcome='An error message is displayed requesting the user to enter their last name.'),\n", " TestCase(requirement_id='597', test_case_description='Verify that entering an invalid state abbreviation displays an error message.', inputs='{ \"state\": \"XX\" }', expected_outcome='An error message is displayed indicating invalid state abbreviation.'),\n", " TestCase(requirement_id='602', test_case_description='Verify that entering an invalid email format displays an error message.', inputs='{ \"email\": \"invalidEmail\" }', expected_outcome='An error message is displayed requesting the user to enter a valid email address.'),\n", " TestCase(requirement_id='599', test_case_description='Verify that submitting the form with an address that cannot be matched displays a pop-up with no suggested address.', inputs='{ \"address\": \"123 Invalid St\" }', expected_outcome='A pop-up is displayed with no suggested address and options to continue or edit the address.'),\n", " TestCase(requirement_id='604', test_case_description='Verify that not selecting an option for the sensor return field displays an error message.', inputs='{ \"sensorReturn\": \"\" }', expected_outcome='An error message is displayed requesting the user to select an option for the sensor return field.')]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["neg_res.test_cases"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["17"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(neg_res.test_cases)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV file './test_cases/negative_test_cases.csv' has been created successfully.\n"]}], "source": ["generate_test_case_csv(test_cases=neg_res.test_cases, prefix='n', output_file='./test_cases/negative_test_cases.csv')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["TestCases(test_cases=[TestCase(requirement_id='480', test_case_description='Verify form data retention after multiple rapid page refreshes.', inputs=\"{ 'form_data': { 'field1': 'value1', 'field2': 'value2' }, 'refresh_count': 10 }\", expected_outcome='Form data remains in the fields, including any error states, after 10 rapid page refreshes.'), TestCase(requirement_id='534', test_case_description=\"Verify sensor serial number field handles maximum length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"{ 'sensor_serial_number': 'A1C2E3G4H5J' }\", expected_outcome='The system accepts the input and displays it in uppercase without any error message.'), TestCase(requirement_id='535', test_case_description='Verify error message for sensor serial number field when input exceeds 11 characters.', inputs=\"{ 'sensor_serial_number': 'A1C2E3G4H5J6' }\", expected_outcome='The system displays an error message indicating to enter a valid sensor serial number and does not allow typing beyond 11 characters.'), TestCase(requirement_id='595', test_case_description='Verify phone number field handles input of exactly 10 numeric characters without spaces or hyphens.', inputs=\"{ 'phone_number': '1234567890' }\", expected_outcome='The system accepts the input without any error message.'), TestCase(requirement_id='596', test_case_description='Verify postal code field handles input of exactly 5 numeric characters.', inputs=\"{ 'postal_code': '12345' }\", expected_outcome='The system accepts the input without any error message.'), TestCase(requirement_id='600', test_case_description='Verify first name field handles input of exactly 40 characters.', inputs=\"{ 'first_name': 'A'.repeat(40) }\", expected_outcome='The system accepts the input without any error message.'), TestCase(requirement_id='601', test_case_description='Verify last name field handles input of exactly 40 characters.', inputs=\"{ 'last_name': 'A'.repeat(40) }\", expected_outcome='The system accepts the input without any error message.'), TestCase(requirement_id='597', test_case_description='Verify state field handles input of invalid 2-character combinations.', inputs=\"{ 'state': 'XX' }\", expected_outcome='The system displays a validation error message requesting to enter the state’s 2-character abbreviation.'), TestCase(requirement_id='602', test_case_description='Verify email field handles input of maximum length with valid format.', inputs=\"{ 'email': 'a'.repeat(64) + '@example.com' }\", expected_outcome='The system accepts the input without any error message.'), TestCase(requirement_id='603', test_case_description=\"Verify 'How many days were you wearing the sensor?' field handles selection of maximum days (15 days for FSL 2 Plus and FSL 3 Plus).\", inputs=\"{ 'days_wearing_sensor': '15' }\", expected_outcome='The system accepts the input and proceeds without any error message.'), TestCase(requirement_id='604', test_case_description=\"Verify 'Is your sensor available for return to Abbott, if requested?' field handles selection of 'No'.\", inputs=\"{ 'sensor_return': 'No' }\", expected_outcome='The system accepts the input and proceeds without any error message.')])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["### Edge test case chain\n", "edge_tst_prompt_template = \"\"\"\n", "You are an LLM agent responsible for generating edge case test scenarios for a project based on the requirements provided in a CSV file. The CSV file contains each requirement in a structured format. Your task is to analyze each requirement and produce edge cases only where applicable, confirming that the system can handle boundary conditions, extreme values, and atypical scenarios appropriately for each relevant requirement.\n", "\n", "Follow these guidelines:\n", "1. Carefully read each requirement from the CSV file.\n", "2. Generate one or more edge cases per requirement if possible. Edge cases should validate that the feature or function works as expected under boundary conditions, extreme values, or rare scenarios, in alignment with the requirement.\n", "3. Each test case should be detailed, specifying edge-case inputs, expected outcomes, and any relevant setup.\n", "4. Skip any requirements for which an edge case is not applicable. Avoid generating standard positive or negative test cases, as these are out of scope for this task.\n", "\n", "Format each test case in this structure:\n", "- Requirement ID: [ID from CSV]\n", "- Test Case Description: [Test case that validates handling of edge-case scenarios]\n", "- Inputs: [List of edge-case inputs for the test case]\n", "- Expected Outcome: [Expected result of the test case]\n", "\n", "Example:\n", "Requirement ID: 001\n", "Test Case Description: \"Verify login handles maximum allowed length for username and password.\"\n", "Inputs: {{\"username\": \"user_\" * 20, \"password\": \"pass_\" * 20}}\n", "Expected Outcome: \"User is successfully logged in if credentials are valid, or receives an appropriate error message if invalid.\"\n", "\n", "Generate a complete list of edge cases only for requirements that allow for edge cases.\n", "Here is the CSV file contents: \n", "{csv_file}\n", "\"\"\"\n", "\n", "edge_tst_prompt = PromptTemplate(input_variables=['csv_file'], template=edge_tst_prompt_template)\n", "\n", "edge_generator = edge_tst_prompt | structured_llm\n", "\n", "# Test it out\n", "edge_res = edge_generator.invoke({\"csv_file\": csv_file_contents})\n", "edge_res"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["[TestCase(requirement_id='480', test_case_description='Verify form data retention after multiple rapid page refreshes.', inputs=\"{ 'form_data': { 'field1': 'value1', 'field2': 'value2' }, 'refresh_count': 10 }\", expected_outcome='Form data remains in the fields, including any error states, after 10 rapid page refreshes.'),\n", " TestCase(requirement_id='534', test_case_description=\"Verify sensor serial number field handles maximum length input with valid characters except 'B', 'I', 'O', and 'S'.\", inputs=\"{ 'sensor_serial_number': 'A1C2E3G4H5J' }\", expected_outcome='The system accepts the input and displays it in uppercase without any error message.'),\n", " TestCase(requirement_id='535', test_case_description='Verify error message for sensor serial number field when input exceeds 11 characters.', inputs=\"{ 'sensor_serial_number': 'A1C2E3G4H5J6' }\", expected_outcome='The system displays an error message indicating to enter a valid sensor serial number and does not allow typing beyond 11 characters.'),\n", " TestCase(requirement_id='595', test_case_description='Verify phone number field handles input of exactly 10 numeric characters without spaces or hyphens.', inputs=\"{ 'phone_number': '1234567890' }\", expected_outcome='The system accepts the input without any error message.'),\n", " TestCase(requirement_id='596', test_case_description='Verify postal code field handles input of exactly 5 numeric characters.', inputs=\"{ 'postal_code': '12345' }\", expected_outcome='The system accepts the input without any error message.'),\n", " TestCase(requirement_id='600', test_case_description='Verify first name field handles input of exactly 40 characters.', inputs=\"{ 'first_name': 'A'.repeat(40) }\", expected_outcome='The system accepts the input without any error message.'),\n", " TestCase(requirement_id='601', test_case_description='Verify last name field handles input of exactly 40 characters.', inputs=\"{ 'last_name': 'A'.repeat(40) }\", expected_outcome='The system accepts the input without any error message.'),\n", " TestCase(requirement_id='597', test_case_description='Verify state field handles input of invalid 2-character combinations.', inputs=\"{ 'state': 'XX' }\", expected_outcome='The system displays a validation error message requesting to enter the state’s 2-character abbreviation.'),\n", " TestCase(requirement_id='602', test_case_description='Verify email field handles input of maximum length with valid format.', inputs=\"{ 'email': 'a'.repeat(64) + '@example.com' }\", expected_outcome='The system accepts the input without any error message.'),\n", " TestCase(requirement_id='603', test_case_description=\"Verify 'How many days were you wearing the sensor?' field handles selection of maximum days (15 days for FSL 2 Plus and FSL 3 Plus).\", inputs=\"{ 'days_wearing_sensor': '15' }\", expected_outcome='The system accepts the input and proceeds without any error message.'),\n", " TestCase(requirement_id='604', test_case_description=\"Verify 'Is your sensor available for return to Abbott, if requested?' field handles selection of 'No'.\", inputs=\"{ 'sensor_return': 'No' }\", expected_outcome='The system accepts the input and proceeds without any error message.')]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["edge_res.test_cases"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["11"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["len(edge_res.test_cases)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV file './test_cases/edge_test_cases.csv' has been created successfully.\n"]}], "source": ["generate_test_case_csv(test_cases=edge_res.test_cases, prefix='e', output_file='./test_cases/edge_test_cases.csv')"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["test_case_id,requirement_id,test_case_description,inputs,expected_outcome,requirement_description\n", "e480_1,480,Verify form data retention after multiple rapid page refreshes.,{ 'form_data': { 'field1': 'value1', 'field2': 'value2' }, 'refresh_count': 10 },Form data remains in the fields, including any error states, after 10 rapid page refreshes.,Given: I have filled out the form fields and/or drop downs of either sensor replacement request formWhen: I refresh the pageThen: the information I added remains in the fields, including any error states\n", "e534_2,534,Verify sensor serial number field handles maximum length input with valid characters except 'B', 'I', 'O', and 'S'.,{ 'sensor_serial_number': 'A1C2E3G4H5J' },The system accepts the input and displays it in uppercase without any error message.,Given User is on the page of the sensor error OR sensor fell off replacement formWhen User is populating the sensor serial number fieldThen it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.\n", "e535_3,535,Verify error message for sensor serial number field when input exceeds 11 characters.,{ 'sensor_serial_number': 'A1C2E3G4H5J6' },The system displays an error message indicating to enter a valid sensor serial number and does not allow typing beyond 11 characters.,Given User on the page of the sensor error OR sensor fell off replacement formWhen User is populating the sensor serial number field with invalid characters or lengthThen there will be an error message displayed on the box, to Enter a valid sensor serial number or it does not allow to type beyond 11 characters.Rule 1:9, 10 and 11 characters are valid sensor number length, any other length should invoke the error message to enter a valid serial number.\n", "e595_4,595,Verify phone number field handles input of exactly 10 numeric characters without spaces or hyphens.,{ 'phone_number': '1234567890' },The system accepts the input without any error message.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the Phone Number fieldThen: The system should show the validation and messaging including: Numeric values only10 digit maxFocus away from field without entering a 10 digit phone number displays error state per the design with an error message to enter 10-digit phone number including area code with no hyphens or spaces.\n", "e596_5,596,Verify postal code field handles input of exactly 5 numeric characters.,{ 'postal_code': '12345' },The system accepts the input without any error message.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the mandatory Postal code fieldThen: The system should show the validation and messaging including: Limit input to 5 numeric characters including the space (cannot type beyond the 5th character)Validation message requesting to enter a valid postal code.\n", "e600_6,600,Verify first name field handles input of exactly 40 characters.,{ 'first_name': 'A'.repeat(40) },The system accepts the input without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory First Name fieldThen: The system should show the validation and messaging including: Allow any character to be entered within field40 character maxFocus away from field without entering anything displays error state per the design with message requesting user to enter their first name.\n", "e601_7,601,Verify last name field handles input of exactly 40 characters.,{ 'last_name': 'A'.repeat(40) },The system accepts the input without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory Last Name fieldThen: The system should show the validation and messaging including: Allow any character to be entered within field40 character maxFocus away from field without entering anything displays error state per the design with message requesting user to enter their last name.\n", "e597_8,597,Verify state field handles input of invalid 2-character combinations.,{ 'state': 'XX' },The system displays a validation error message requesting to enter the state’s 2-character abbreviation.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the State fieldThen: The system should show the validation and messaging including: Only accepts 2 alpha characters inputMust be one of the following two alpha character combinations:AL, AK, AZ, AR, CA, CO, CT, DE, DC, FL, GA, HI, ID, IL, IN, IA, KS, KY, LA, ME, MT, NE, NV, NH, NJ, NM, NY, NC, ND, OH, OK, OR, MD, MA, MI, MN, MS, MO, PA, RI, SC, SD, TN, TX, UT, VT, VA, WA, WV, WI, WY, MP, VI, PR, MH, AA, AE, AP, GU, AS, FM, PW, UMValidation error message should request to enter state’s 2-character abbreviation.\n", "e602_9,602,Verify email field handles input of maximum length with valid format.,{ 'email': 'a'.repeat(64) + '@example.com' },The system accepts the input without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory email fieldThen: The system should show the validation and messaging including: A valid format of emailFocus away from field without entering a valid email displays error state per the design with message requesting user to enter a valid email address.\n", "e603_10,603,Verify 'How many days were you wearing the sensor?' field handles selection of maximum days (15 days for FSL 2 Plus and FSL 3 Plus).,{ 'days_wearing_sensor': '15' },The system accepts the input and proceeds without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory How many days were you wearing the sensor? fieldThen: The system should show the options (based on the product chosen): I'm not sureSame day1 day2 days3 days…14 days15 days (Applicable for FSL 2 Plus and FSL 3 Plus only)\n", "e604_11,604,Verify 'Is your sensor available for return to Abbott, if requested?' field handles selection of 'No'.,{ 'sensor_return': 'No' },The system accepts the input and proceeds without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory Is your sensor available for return to Abbott, if requested? drop down fieldThen: The system should show the below options: YesNo\n"]}], "source": ["def csv_to_string(file_path):\n", "    # Load the CSV file into a DataFrame\n", "    df = pd.read_csv(file_path)\n", "\n", "    # Remove \\n and \\r characters from the requirement_description column\n", "    df['requirement_description'] = df['requirement_description'].str.replace(r'[\\n\\r]', '', regex=True)\n", "\n", "    # Initialize an empty list to store each row as a comma-separated string\n", "    rows = []\n", "    rows.append(\"test_case_id,requirement_id,test_case_description,inputs,expected_outcome,requirement_description\")\n", "    # Iterate through each row and join values with commas, without spaces\n", "    for _, row in df.iterrows():\n", "        row_string = ','.join(str(value) for value in row)\n", "        rows.append(row_string)\n", "\n", "    # Join all rows with a newline character\n", "    final_string = '\\n'.join(rows)\n", "\n", "    return final_string\n", "\n", "csv_string = csv_to_string(file_path = './test_cases/edge_test_cases.csv')\n", "print(csv_string)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# def get_test_desc_string(res, data):\n", "#     test_cases_dict = [case.dict() for case in res.test_cases]\n", "\n", "#     # Add descriptions based on 'requirement_id' from the DataFrame to each test case\n", "#     for case in test_cases_dict:\n", "#         description = data.loc[data['Req ID'] == int(case['requirement_id']), 'Description'].values\n", "#         if description.size > 0:  # Check if the array is not empty\n", "#             cleaned_description = description[0].replace('\\n', '').replace('\\r', '')\n", "#             case['requirement_description'] = cleaned_description\n", "\n", "#     output = io.StringIO()\n", "#     writer = csv.DictWriter(output, fieldnames=test_cases_dict[0].keys())\n", "#     writer.writeheader()\n", "#     writer.writerows(test_cases_dict)\n", "\n", "#     # Get the CSV content as a string\n", "#     csv_string = output.getvalue()\n", "#     output.close()\n", "#     return csv_string\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["test_case_id,requirement_id,test_case_description,inputs,expected_outcome,requirement_description\n", "p480_1,480,Verify that the form data remains after refreshing the page.,Fill out the form fields and/or drop downs of the sensor replacement request form, then refresh the page.,The information added remains in the fields, including any error states.,Given: I have filled out the form fields and/or drop downs of either sensor replacement request formWhen: I refresh the pageThen: the information I added remains in the fields, including any error states\n", "p538_2,538,Verify that the eligibility page is rendered with the correct options and rules.,Navigate to Sensor Replacement Request from the Error message flow.,The eligibility page is rendered with a checkbox to acknowledge privacy agreement, a submit button, and the rules are enforced (privacy agreement must be acknowledged before submit button is enabled, and clicking the link directs to Abbott Privacy Resources).,Given: User is on Guided Support ExperienceWhen: User is navigated to Sensor Replacement Request from the Error message flowThen: System will render eligibility page with following options:Checkbox to acknowledge privacy agreementSubmit buttonRules list:R1: User must agree to privacy acknowledgement prior to submit button becoming enabledR2: If user clicks link at the end of the paragraph on sensitive data collection and usage, then they are directed to Abbott Privacy Resources\n", "p594_3,594,Verify that address suggestions are shown and fields are populated correctly.,Start typing in the Street Address Line 1 field on the Your Information page.,Address suggestions are shown in a drop-down, and selecting an option populates Street address line 1, Street address line 2 (if applicable), City, State, and Postal Code fields.,Given: The User is on the Your Information page on the Sensor Fell Off/Error message formsWhen: The User starts typing in the Street Address Line 1 fieldThen: The system should show address suggestions in a drop-downAnd: When the User selects an option, the fields below are populated:Street address line 1Street address line 2 (if applicable)CityStatePostal Code\n", "p534_4,534,Verify that the sensor serial number field accepts valid alphanumeric characters and lengths.,Enter a valid sensor serial number (9, 10, or 11 characters long, excluding 'B', 'I', 'O', and 'S').,The sensor serial number is accepted and displayed in uppercase.,Given User is on the page of the sensor error OR sensor fell off replacement formWhen User is populating the sensor serial number fieldThen it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.\n", "p595_5,595,Verify that the phone number field validates input correctly.,Enter a 10-digit phone number without hyphens or spaces in the Phone Number field and focus away from the field.,The phone number is accepted without error.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the Phone Number fieldThen: The system should show the validation and messaging including: Numeric values only10 digit maxFocus away from field without entering a 10 digit phone number displays error state per the design with an error message to enter 10-digit phone number including area code with no hyphens or spaces.\n", "p539_6,539,Verify that the sensor information page is rendered with the correct fields and rules.,Acknowledge and submit the eligibility form.,The sensor information page is rendered with fields for Sensor Serial Number, How many days were you wearing the sensor?, Is your sensor available for return to <PERSON>?, and What is the message event code?. All required fields must be completed with valid entries before the next button is enabled.,Given: User is on Guided Support ExperienceWhen: User acknowledges and submits eligibility formThen: System will render sensor information page with following fields:Sensor Serial NumberHow many days were you wearing the sensor?Is your sensor available for return to Abbott?What is the message event code?Rules list:R1. User must complete all required fields with valid entries prior to next button becoming enabled\n", "p482_7,482,Verify that previously entered data is retained when changing the URL within the same locale.,Enter data in the Sensor fall off or Sensor error message webform, change the URL to GSE guided questions of the same locale, and navigate forward to the fall off or error message webform.,Previously entered data is retained in the relevant fields.,Given I am on the Sensor fall off or Senor error message webformWhen I change the URL to GSE guided questions of the same localeAnd I navigate forward to the fall off or error message webformThen my previously entered data will be retained in the relevant fields\n", "p540_8,540,Verify that the Your Information page is rendered with the correct fields and rules.,Complete and submit the sensor information page.,The Your Information page is rendered with fields for First Name, Last Name, Phone, Email, Street Address Line 1, Street Address Line 2, City, State, and Postal Code. All required fields must be completed with valid entries before the submit button is enabled.,Given: User is on Guided Support ExperienceWhen: User completes and submits sensor information pageThen: System will render your information page with following fields:First NameLast NamePhoneEmailStreet Address Line 1Street Address Line 2CityStatePostal CodeRules list:R1. User must complete all required fields with valid entries prior to submit button becoming enabled\n", "p596_9,596,Verify that the postal code field validates input correctly.,Enter a 5-digit postal code in the Postal code field.,The postal code is accepted without error.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the mandatory Postal code fieldThen: The system should show the validation and messaging including: Limit input to 5 numeric characters including the space (cannot type beyond the 5th character)Validation message requesting to enter a valid postal code.\n", "p600_10,600,Verify that the first name field validates input correctly.,Enter a valid first name (up to 40 characters) in the First Name field and focus away from the field.,The first name is accepted without error.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory First Name fieldThen: The system should show the validation and messaging including: Allow any character to be entered within field40 character maxFocus away from field without entering anything displays error state per the design with message requesting user to enter their first name.\n", "p601_11,601,Verify that the last name field validates input correctly.,Enter a valid last name (up to 40 characters) in the Last Name field and focus away from the field.,The last name is accepted without error.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory Last Name fieldThen: The system should show the validation and messaging including: Allow any character to be entered within field40 character maxFocus away from field without entering anything displays error state per the design with message requesting user to enter their last name.\n", "p597_12,597,Verify that the state field validates input correctly.,Enter a valid 2-character state abbreviation in the State field.,The state abbreviation is accepted without error.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the State fieldThen: The system should show the validation and messaging including: Only accepts 2 alpha characters inputMust be one of the following two alpha character combinations:AL, AK, AZ, AR, CA, CO, CT, DE, DC, FL, GA, HI, ID, IL, IN, IA, KS, KY, LA, ME, MT, NE, NV, NH, NJ, NM, NY, NC, ND, OH, OK, OR, MD, MA, MI, MN, MS, MO, PA, RI, SC, SD, TN, TX, UT, VT, VA, WA, WV, WI, WY, MP, VI, PR, MH, AA, AE, AP, GU, AS, FM, PW, UMValidation error message should request to enter state’s 2-character abbreviation.\n", "p541_13,541,Verify that the confirmation page is rendered after successful submission.,Complete and submit the Your Information page.,The confirmation page is rendered with submission confirmation and information on what happens next, including processing time. Clicking on the Customer Support phone number invokes the browser call functionality.,Given: User is on Guided Support ExperienceWhen: User completes and submits your information pageThen: System will render a confirmation page with submission confirmation and information on what happens next including processing time.Rules list:R1. clicking on the Customer Support phone number (************) will invoke the browser call functionality\n", "p576_14,576,Verify that the user is directed to the sensor error replacement request form after accepting the privacy agreement and clicking Submit.,Accept the privacy agreement and click Submit on the Eligibility page.,The user is directed to the sensor error replacement request form.,Given: User is on the Eligibility pageWhen: User accepts the privacy agreement and clicks SubmitThen: System will direct user to sensor error replacement request form\n", "p598_15,598,Verify that a pop-up modal with a suggested address is displayed when the address validation service returns a new selection.,Complete all required address fields with an address that is not an exact match with the address validation service and click submit.,A pop-up modal with a suggested address is displayed for the user to either select or continue with the address currently in the fields, as well as an Edit link, and a disabled submit button. If the user makes a selection and clicks submit, the submit button becomes enabled and the form can be submitted with the selected address. If the user clicks on the edit button, they are returned to the form.,Given: User has completed all required address fields on the Sensor Fell Off/Error message forms and they are not an exact match with the address validation service When: User clicks submit on the formThen: a pop-up modal with a suggested address is displayed for the user to either select or continue with the address currently in the fields, as well as an Edit link, and a disabled submit buttonRules list:R1. If user makes a selection and clicks submit,&nbsp; the submit button becomes enabled and the form can be submitted with the selected addressR2. If User clicks on edit button, then user is returned to the form\n", "p602_16,602,Verify that the email field validates input correctly.,Enter a valid email address in the Email field and focus away from the field.,The email address is accepted without error.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory email fieldThen: The system should show the validation and messaging including: A valid format of emailFocus away from field without entering a valid email displays error state per the design with message requesting user to enter a valid email address.\n", "p603_17,603,Verify that the 'How many days were you wearing the sensor?' field shows the correct options.,Interact with the 'How many days were you wearing the sensor?' field.,The field shows the options: I'm not sure, Same day, 1 day, 2 days, 3 days, …, 14 days, 15 days (Applicable for FSL 2 Plus and FSL 3 Plus only).,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory How many days were you wearing the sensor? fieldThen: The system should show the options (based on the product chosen): I'm not sureSame day1 day2 days3 days…14 days15 days (Applicable for FSL 2 Plus and FSL 3 Plus only)\n", "p604_18,604,Verify that the 'Is your sensor available for return to Abbott, if requested?' field shows the correct options.,Interact with the 'Is your sensor available for return to Abbott, if requested?' drop down field.,The field shows the options: Yes, No.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory Is your sensor available for return to Abbott, if requested? drop down fieldThen: The system should show the below options: YesNo\n"]}], "source": ["csv_string = csv_to_string(file_path = './test_cases/positive_test_cases.csv')\n", "print(csv_string)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["### Feedback Chain\n", "class FeedbackTemplate(BaseModel):\n", "    test_case_id: str = Field(description=\"The ID of the test case which is incorrect, incomplete, or contains inaccuracies or irrelevant details.\")\n", "    feedback: str = Field(description=\"Detailed feedback identifying the issues in the test case, including what is incorrect, missing, or needs modification.\")\n", "\n", "class Feedback(BaseModel):\n", "    feedback_list: List[FeedbackTemplate] = Field(description=\"A list of feedback entries, each identifying issues in specific test cases that are incorrect, incomplete, or contain inaccuracies. Each entry includes the requirement ID and corresponding feedback details.\")\n", "\n", "\n", "feedback_prompt_template = \"\"\"\n", "You are an LLM agent responsible for reviewing {test_case_type} test cases generated for a project based on specific requirements. Your task is to analyze each test case against the associated requirement and provide feedback only for cases that are incorrect, incomplete, or contain inaccuracies or irrelevant details.\n", "\n", "Follow these guidelines:\n", "1. Review each requirement and its corresponding test cases.\n", "2. Identify test cases that need correction due to missing aspects, inaccuracies, or irrelevant information (hallucinations).\n", "3. For each test case that requires correction, provide concise feedback that specifies:\n", "    - The requirement ID.\n", "    - A brief but clear explanation of the issue in the test case, including missing details, inaccuracies, or suggestions for improvement.\n", "\n", "Format your feedback in the structure of a `Feedback` model:\n", "- feedback_list: [\n", "    {{\n", "        \"test_case_id\": \"test case ID where an issue is identified\",\n", "        \"feedback\": \"Detailed feedback on what is wrong with the test case and suggested modifications.\"\n", "    }},\n", "    ...\n", "]\n", "\n", "Only provide feedback for test cases that need correction; omit feedback for cases that are accurate and complete.\n", "\n", "Example:\n", "feedback_list: [\n", "    {{\n", "        \"test_case_id\": \"p20_1\",\n", "        \"feedback\": \"Test case does not account for server downtime. Suggest adding a scenario to handle service unavailability.\"\n", "    }},\n", "    {{\n", "        \"test_case_id\": \"e890_4\",\n", "        \"feedback\": \"Unnecessary details in the test case that are not relevant to the requirement. Simplify the test to focus on core functionality.\"\n", "    }}\n", "]\n", "Here are the generated test cases:\n", "{generated_test_cases}\n", "\"\"\"\n", "\n", "# Initialize the llm\n", "llm = AzureChatOpenAI(model='gpt-4o', temperature=0)\n", "structured_llm = llm.with_structured_output(Feedback)\n", "feedback_tst_prompt = PromptTemplate(input_variables=['test_case_type', 'generated_test_cases'], template=feedback_prompt_template)\n", "feedback_generator = feedback_tst_prompt | structured_llm\n", "\n", "# Test it out\n", "feedback_res = feedback_generator.invoke({\"test_case_type\": \"positive\", \"generated_test_cases\": csv_string})"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["[FeedbackTemplate(test_case_id='p480_1', feedback='The test case does not specify that the form data should be retained even after navigating away from the page and returning. Suggest adding a scenario to handle navigation away and back to the form.'),\n", " FeedbackTemplate(test_case_id='p538_2', feedback='The test case does not mention the need to verify the enforcement of the rules (e.g., privacy agreement acknowledgment before enabling the submit button). Suggest adding steps to check rule enforcement.'),\n", " FeedbackTemplate(test_case_id='p534_4', feedback='The test case does not specify the behavior when invalid characters or lengths are entered. Suggest adding scenarios for invalid inputs to ensure comprehensive validation.'),\n", " FeedbackTemplate(test_case_id='p595_5', feedback='The test case does not cover the scenario where the user enters an invalid phone number. Suggest adding steps to verify error messaging for invalid phone numbers.'),\n", " FeedbackTemplate(test_case_id='p539_6', feedback='The test case does not specify the behavior when invalid entries are made in the required fields. Suggest adding scenarios for invalid inputs to ensure comprehensive validation.'),\n", " FeedbackTemplate(test_case_id='p596_9', feedback='The test case does not cover the scenario where the user enters an invalid postal code. Suggest adding steps to verify error messaging for invalid postal codes.'),\n", " FeedbackTemplate(test_case_id='p600_10', feedback='The test case does not cover the scenario where the user enters an invalid first name. Suggest adding steps to verify error messaging for invalid first names.'),\n", " FeedbackTemplate(test_case_id='p601_11', feedback='The test case does not cover the scenario where the user enters an invalid last name. Suggest adding steps to verify error messaging for invalid last names.'),\n", " FeedbackTemplate(test_case_id='p597_12', feedback='The test case does not cover the scenario where the user enters an invalid state abbreviation. Suggest adding steps to verify error messaging for invalid state abbreviations.'),\n", " FeedbackTemplate(test_case_id='p602_16', feedback='The test case does not cover the scenario where the user enters an invalid email address. Suggest adding steps to verify error messaging for invalid email addresses.')]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["feedback_res.feedback_list"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["test_case_id,requirement_id,test_case_description,inputs,expected_outcome,requirement_description\n", "n480_1,480,Verify that refreshing the page with invalid form data retains the invalid data and error states.,{ \"formData\": { \"field1\": \"invalidData\", \"field2\": \"\" }, \"action\": \"refreshPage\" },The form retains the invalid data and displays the appropriate error messages.,Given: I have filled out the form fields and/or drop downs of either sensor replacement request formWhen: I refresh the pageThen: the information I added remains in the fields, including any error states\n", "n538_2,538,Verify that the submit button remains disabled if the user does not agree to the privacy acknowledgement.,{ \"privacyAcknowledgement\": false },The submit button remains disabled and the user cannot proceed.,Given: User is on Guided Support ExperienceWhen: User is navigated to Sensor Replacement Request from the Error message flowThen: System will render eligibility page with following options:Checkbox to acknowledge privacy agreementSubmit buttonRules list:R1: User must agree to privacy acknowledgement prior to submit button becoming enabledR2: If user clicks link at the end of the paragraph on sensitive data collection and usage, then they are directed to Abbott Privacy Resources\n", "n594_3,594,Verify that entering an invalid address does not populate the address fields.,{ \"addressInput\": \"Invalid Address\" },The address fields remain empty and an error message is displayed.,Given: The User is on the Your Information page on the Sensor Fell Off/Error message formsWhen: The User starts typing in the Street Address Line 1 fieldThen: The system should show address suggestions in a drop-downAnd: When the User selects an option, the fields below are populated:Street address line 1Street address line 2 (if applicable)CityStatePostal Code\n", "n534_4,534,Verify that entering invalid characters in the sensor serial number field displays an error message.,{ \"sensorSerialNumber\": \"12345B7890\" },An error message is displayed indicating invalid characters.,Given User is on the page of the sensor error OR sensor fell off replacement formWhen User is populating the sensor serial number fieldThen it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.\n", "n535_5,535,Verify that entering a sensor serial number with invalid length displays an error message.,{ \"sensorSerialNumber\": \"12345678\" },An error message is displayed indicating invalid length.,Given User on the page of the sensor error OR sensor fell off replacement form<PERSON>hen User is populating the sensor serial number field with invalid characters or lengthThen there will be an error message displayed on the box, to Enter a valid sensor serial number or it does not allow to type beyond 11 characters.Rule 1:9, 10 and 11 characters are valid sensor number length, any other length should invoke the error message to enter a valid serial number.\n", "n595_6,595,Verify that entering a phone number with non-numeric characters displays an error message.,{ \"phoneNumber\": \"************\" },An error message is displayed indicating invalid phone number format.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the Phone Number fieldThen: The system should show the validation and messaging including: Numeric values only10 digit maxFocus away from field without entering a 10 digit phone number displays error state per the design with an error message to enter 10-digit phone number including area code with no hyphens or spaces.\n", "n539_7,539,Verify that the next button remains disabled if required fields are not completed with valid entries.,{ \"requiredFields\": { \"sensorSerialNumber\": \"\", \"daysWorn\": \"\" } },The next button remains disabled and the user cannot proceed.,Given: User is on Guided Support ExperienceWhen: User acknowledges and submits eligibility formThen: System will render sensor information page with following fields:Sensor Serial NumberHow many days were you wearing the sensor?Is your sensor available for return to Abbott?What is the message event code?Rules list:R1. User must complete all required fields with valid entries prior to next button becoming enabled\n", "n481_8,481,Verify that changing the sensor replacement type clears the form data.,{ \"formData\": { \"field1\": \"data1\", \"field2\": \"data2\" }, \"action\": \"changeReplacementType\" },The form data is cleared and the fields are empty.,Given: I have filled out the form fields and/or drop downs of either sensor replacement request formWhen: I change sensor replacement type (from error message to fell off or vice versa) and revisit the formThen: the information is cleared\n", "n482_9,482,Verify that changing the URL within the same locale retains the previously entered data.,{ \"url\": \"newURL\", \"formData\": { \"field1\": \"data1\", \"field2\": \"data2\" } },The previously entered data is retained in the relevant fields.,Given I am on the Sensor fall off or Senor error message webformWhen I change the URL to GSE guided questions of the same localeAnd I navigate forward to the fall off or error message webformThen my previously entered data will be retained in the relevant fields\n", "n540_10,540,Verify that the submit button remains disabled if required fields are not completed with valid entries.,{ \"requiredFields\": { \"firstName\": \"\", \"lastName\": \"\" } },The submit button remains disabled and the user cannot proceed.,Given: User is on Guided Support ExperienceWhen: User completes and submits sensor information pageThen: System will render your information page with following fields:First NameLast NamePhoneEmailStreet Address Line 1Street Address Line 2CityStatePostal CodeRules list:R1. User must complete all required fields with valid entries prior to submit button becoming enabled\n", "n596_11,596,Verify that entering a postal code with more than 5 characters displays an error message.,{ \"postalCode\": \"123456\" },An error message is displayed indicating invalid postal code length.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the mandatory Postal code fieldThen: The system should show the validation and messaging including: Limit input to 5 numeric characters including the space (cannot type beyond the 5th character)Validation message requesting to enter a valid postal code.\n", "n600_12,600,Verify that leaving the first name field empty displays an error message.,{ \"firstName\": \"\" },An error message is displayed requesting the user to enter their first name.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory First Name fieldThen: The system should show the validation and messaging including: Allow any character to be entered within field40 character maxFocus away from field without entering anything displays error state per the design with message requesting user to enter their first name.\n", "n601_13,601,Verify that leaving the last name field empty displays an error message.,{ \"lastName\": \"\" },An error message is displayed requesting the user to enter their last name.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory Last Name fieldThen: The system should show the validation and messaging including: Allow any character to be entered within field40 character maxFocus away from field without entering anything displays error state per the design with message requesting user to enter their last name.\n", "n597_14,597,Verify that entering an invalid state abbreviation displays an error message.,{ \"state\": \"XX\" },An error message is displayed indicating invalid state abbreviation.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the State fieldThen: The system should show the validation and messaging including: Only accepts 2 alpha characters inputMust be one of the following two alpha character combinations:AL, AK, AZ, AR, CA, CO, CT, DE, DC, FL, GA, HI, ID, IL, IN, IA, KS, KY, LA, ME, MT, NE, NV, NH, NJ, NM, NY, NC, ND, OH, OK, OR, MD, MA, MI, MN, MS, MO, PA, RI, SC, SD, TN, TX, UT, VT, VA, WA, WV, WI, WY, MP, VI, PR, MH, AA, AE, AP, GU, AS, FM, PW, UMValidation error message should request to enter state’s 2-character abbreviation.\n", "n602_15,602,Verify that entering an invalid email format displays an error message.,{ \"email\": \"invalidEmail\" },An error message is displayed requesting the user to enter a valid email address.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory email fieldThen: The system should show the validation and messaging including: A valid format of emailFocus away from field without entering a valid email displays error state per the design with message requesting user to enter a valid email address.\n", "n599_16,599,Verify that submitting the form with an address that cannot be matched displays a pop-up with no suggested address.,{ \"address\": \"123 Invalid St\" },A pop-up is displayed with no suggested address and options to continue or edit the address.,Given: The User has completed all required address fields on Sensor Fell Off/Error message form and the address validation service does not have a matching addressWhen: The User submits the formThen: a pop-up with no suggested address is displayed to either continue with the address as written or make changes to their input through an Edit linkRules list:&nbsp;&nbsp;&nbsp; R1. If user opts to continue with entered address and clicks submit,&nbsp;the form is submitted with selected address&nbsp;&nbsp;&nbsp; R2. If User clicks on edit button, then user is returned to the form\n", "n604_17,604,Verify that not selecting an option for the sensor return field displays an error message.,{ \"sensorReturn\": \"\" },An error message is displayed requesting the user to select an option for the sensor return field.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory Is your sensor available for return to Abbott, if requested? drop down fieldThen: The system should show the below options: YesNo\n"]}], "source": ["csv_string = csv_to_string(file_path = './test_cases/negative_test_cases.csv')\n", "print(csv_string)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["feedback_res = feedback_generator.invoke({\"test_case_type\": \"negative\", \"generated_test_cases\": csv_string})"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["[FeedbackTemplate(test_case_id='n480_1', feedback='The test case does not specify that the form should retain the error states along with the invalid data after refreshing the page. Suggest adding this detail to ensure completeness.'),\n", " FeedbackTemplate(test_case_id='n538_2', feedback='The test case does not cover the scenario where the user clicks the link at the end of the paragraph on sensitive data collection and usage. Suggest adding this scenario to ensure all rules are covered.'),\n", " FeedbackTemplate(test_case_id='n594_3', feedback='The test case does not specify that address suggestions should be shown in a drop-down when the user starts typing. Suggest adding this detail to align with the requirement.'),\n", " FeedbackTemplate(test_case_id='n534_4', feedback='The test case does not specify that the error message should indicate which characters are invalid. Suggest adding this detail to ensure clarity.'),\n", " FeedbackTemplate(test_case_id='n535_5', feedback='The test case does not specify that the error message should indicate the valid length for the sensor serial number. Suggest adding this detail to ensure clarity.'),\n", " FeedbackTemplate(test_case_id='n595_6', feedback='The test case does not specify that the phone number should not include hyphens or spaces. Suggest adding this detail to align with the requirement.'),\n", " FeedbackTemplate(test_case_id='n539_7', feedback='The test case does not specify that the required fields must be completed with valid entries. Suggest adding this detail to ensure completeness.'),\n", " FeedbackTemplate(test_case_id='n540_10', feedback='The test case does not specify that the required fields must be completed with valid entries. Suggest adding this detail to ensure completeness.'),\n", " FeedbackTemplate(test_case_id='n596_11', feedback='The test case does not specify that the postal code should be limited to 5 numeric characters. Suggest adding this detail to align with the requirement.'),\n", " FeedbackTemplate(test_case_id='n600_12', feedback='The test case does not specify that the first name field should allow any character to be entered within the 40 character max limit. Suggest adding this detail to ensure completeness.'),\n", " FeedbackTemplate(test_case_id='n601_13', feedback='The test case does not specify that the last name field should allow any character to be entered within the 40 character max limit. Suggest adding this detail to ensure completeness.'),\n", " FeedbackTemplate(test_case_id='n597_14', feedback='The test case does not specify that the state abbreviation must be one of the valid two alpha character combinations. Suggest adding this detail to ensure completeness.'),\n", " FeedbackTemplate(test_case_id='n602_15', feedback='The test case does not specify that the email field should allow only a valid email format. Suggest adding this detail to ensure completeness.'),\n", " FeedbackTemplate(test_case_id='n599_16', feedback='The test case does not specify that the user should have options to continue with the address as written or make changes through an Edit link. Suggest adding this detail to ensure completeness.'),\n", " FeedbackTemplate(test_case_id='n604_17', feedback='The test case does not specify that the error message should request the user to select an option for the sensor return field. Suggest adding this detail to ensure clarity.')]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["feedback_res.feedback_list"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["test_case_id,requirement_id,test_case_description,inputs,expected_outcome,requirement_description\n", "e480_1,480,Verify form data retention after multiple rapid page refreshes.,{ 'form_data': { 'field1': 'value1', 'field2': 'value2' }, 'refresh_count': 10 },Form data remains in the fields, including any error states, after 10 rapid page refreshes.,Given: I have filled out the form fields and/or drop downs of either sensor replacement request formWhen: I refresh the pageThen: the information I added remains in the fields, including any error states\n", "e534_2,534,Verify sensor serial number field handles maximum length input with valid characters except 'B', 'I', 'O', and 'S'.,{ 'sensor_serial_number': 'A1C2E3G4H5J' },The system accepts the input and displays it in uppercase without any error message.,Given User is on the page of the sensor error OR sensor fell off replacement formWhen User is populating the sensor serial number fieldThen it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.\n", "e535_3,535,Verify error message for sensor serial number field when input exceeds 11 characters.,{ 'sensor_serial_number': 'A1C2E3G4H5J6' },The system displays an error message indicating to enter a valid sensor serial number and does not allow typing beyond 11 characters.,Given User on the page of the sensor error OR sensor fell off replacement formWhen User is populating the sensor serial number field with invalid characters or lengthThen there will be an error message displayed on the box, to Enter a valid sensor serial number or it does not allow to type beyond 11 characters.Rule 1:9, 10 and 11 characters are valid sensor number length, any other length should invoke the error message to enter a valid serial number.\n", "e595_4,595,Verify phone number field handles input of exactly 10 numeric characters without spaces or hyphens.,{ 'phone_number': '1234567890' },The system accepts the input without any error message.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the Phone Number fieldThen: The system should show the validation and messaging including: Numeric values only10 digit maxFocus away from field without entering a 10 digit phone number displays error state per the design with an error message to enter 10-digit phone number including area code with no hyphens or spaces.\n", "e596_5,596,Verify postal code field handles input of exactly 5 numeric characters.,{ 'postal_code': '12345' },The system accepts the input without any error message.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the mandatory Postal code fieldThen: The system should show the validation and messaging including: Limit input to 5 numeric characters including the space (cannot type beyond the 5th character)Validation message requesting to enter a valid postal code.\n", "e600_6,600,Verify first name field handles input of exactly 40 characters.,{ 'first_name': 'A'.repeat(40) },The system accepts the input without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory First Name fieldThen: The system should show the validation and messaging including: Allow any character to be entered within field40 character maxFocus away from field without entering anything displays error state per the design with message requesting user to enter their first name.\n", "e601_7,601,Verify last name field handles input of exactly 40 characters.,{ 'last_name': 'A'.repeat(40) },The system accepts the input without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory Last Name fieldThen: The system should show the validation and messaging including: Allow any character to be entered within field40 character maxFocus away from field without entering anything displays error state per the design with message requesting user to enter their last name.\n", "e597_8,597,Verify state field handles input of invalid 2-character combinations.,{ 'state': 'XX' },The system displays a validation error message requesting to enter the state’s 2-character abbreviation.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the State fieldThen: The system should show the validation and messaging including: Only accepts 2 alpha characters inputMust be one of the following two alpha character combinations:AL, AK, AZ, AR, CA, CO, CT, DE, DC, FL, GA, HI, ID, IL, IN, IA, KS, KY, LA, ME, MT, NE, NV, NH, NJ, NM, NY, NC, ND, OH, OK, OR, MD, MA, MI, MN, MS, MO, PA, RI, SC, SD, TN, TX, UT, VT, VA, WA, WV, WI, WY, MP, VI, PR, MH, AA, AE, AP, GU, AS, FM, PW, UMValidation error message should request to enter state’s 2-character abbreviation.\n", "e602_9,602,Verify email field handles input of maximum length with valid format.,{ 'email': 'a'.repeat(64) + '@example.com' },The system accepts the input without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory email fieldThen: The system should show the validation and messaging including: A valid format of emailFocus away from field without entering a valid email displays error state per the design with message requesting user to enter a valid email address.\n", "e603_10,603,Verify 'How many days were you wearing the sensor?' field handles selection of maximum days (15 days for FSL 2 Plus and FSL 3 Plus).,{ 'days_wearing_sensor': '15' },The system accepts the input and proceeds without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory How many days were you wearing the sensor? fieldThen: The system should show the options (based on the product chosen): I'm not sureSame day1 day2 days3 days…14 days15 days (Applicable for FSL 2 Plus and FSL 3 Plus only)\n", "e604_11,604,Verify 'Is your sensor available for return to Abbott, if requested?' field handles selection of 'No'.,{ 'sensor_return': 'No' },The system accepts the input and proceeds without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory Is your sensor available for return to Abbott, if requested? drop down fieldThen: The system should show the below options: YesNo\n"]}], "source": ["csv_string = csv_to_string(file_path = './test_cases/edge_test_cases.csv')\n", "print(csv_string)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["feedback_res = feedback_generator.invoke({\"test_case_type\": \"edge\", \"generated_test_cases\": csv_string})"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["[FeedbackTemplate(test_case_id='e534_2', feedback=\"The test case does not specify the behavior when invalid characters ('B', 'I', 'O', 'S') are entered. Suggest adding a scenario to handle invalid characters.\"),\n", " FeedbackTemplate(test_case_id='e595_4', feedback='The test case does not cover the scenario where the user enters fewer than 10 digits. Suggest adding a scenario to handle input with fewer than 10 digits.'),\n", " FeedbackTemplate(test_case_id='e596_5', feedback='The test case does not specify the behavior when the input is less than 5 characters. Suggest adding a scenario to handle input with fewer than 5 characters.'),\n", " FeedbackTemplate(test_case_id='e602_9', feedback='The test case does not specify the behavior when the email format is invalid. Suggest adding a scenario to handle invalid email formats.'),\n", " FeedbackTemplate(test_case_id='e603_10', feedback='The test case does not specify the behavior for other products that are not FSL 2 Plus or FSL 3 Plus. Suggest adding scenarios for other products.')]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["feedback_res.feedback_list"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["def get_feedback_str(feedback_lst):\n", "    s = \"test_case_id,feedback\\n\"\n", "    for feedback in feedback_lst:\n", "        s += feedback.test_case_id + \",\" + feedback.feedback + \"\\n\"\n", "    return s\n", "fb = get_feedback_str(feedback_lst=feedback_res.feedback_list)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["test_case_id,feedback\n", "e534_2,The test case does not specify the behavior when invalid characters ('B', 'I', 'O', 'S') are entered. Suggest adding a scenario to handle invalid characters.\n", "e595_4,The test case does not cover the scenario where the user enters fewer than 10 digits. Suggest adding a scenario to handle input with fewer than 10 digits.\n", "e596_5,The test case does not specify the behavior when the input is less than 5 characters. Suggest adding a scenario to handle input with fewer than 5 characters.\n", "e602_9,The test case does not specify the behavior when the email format is invalid. Suggest adding a scenario to handle invalid email formats.\n", "e603_10,The test case does not specify the behavior for other products that are not FSL 2 Plus or FSL 3 Plus. Suggest adding scenarios for other products.\n", "\n"]}], "source": ["print(fb)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def get_filtered_generated_test_cases(res_file, feedback_res):\n", "    test_case_ids = []\n", "    for feedback in feedback_res.feedback_list:\n", "        test_case_ids.append(feedback.test_case_id)\n", "    df = pd.read_csv(res_file)\n", "    # Remove \\n and \\r characters from the requirement_description column\n", "    df['requirement_description'] = df['requirement_description'].str.replace(r'[\\n\\r]', '', regex=True)\n", "\n", "    # Initialize an empty list to store each row as a comma-separated string\n", "    rows = []\n", "    rows.append(\"test_case_id,requirement_id,test_case_description,inputs,expected_outcome,requirement_description\")\n", "    \n", "    for _, row in df.iterrows():\n", "        if row['test_case_id'] in test_case_ids:\n", "            rows.append(f\"{row['test_case_id']},{row['requirement_id']},{row['test_case_description']},{row['inputs']},{row['expected_outcome']},{row['requirement_description']}\")\n", "    \n", "    # Join all rows into a single CSV-formatted string\n", "    csv_output = \"\\n\".join(rows)\n", "    \n", "    return csv_output\n", "test_cases_fb = get_filtered_generated_test_cases(\"./test_cases/edge_test_cases.csv\", feedback_res) \n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["test_case_id,requirement_id,test_case_description,inputs,expected_outcome,requirement_description\n", "e534_2,534,Verify sensor serial number field handles maximum length input with valid characters except 'B', 'I', 'O', and 'S'.,{ 'sensor_serial_number': 'A1C2E3G4H5J' },The system accepts the input and displays it in uppercase without any error message.,Given User is on the page of the sensor error OR sensor fell off replacement formWhen User is populating the sensor serial number fieldThen it should allow them to enter alphanumeric characters 9, 10 or 11 characters long with an exception of “B”, “I”, “O” and “S” and characters will always be displayed in uppercase.\n", "e595_4,595,Verify phone number field handles input of exactly 10 numeric characters without spaces or hyphens.,{ 'phone_number': '1234567890' },The system accepts the input without any error message.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the Phone Number fieldThen: The system should show the validation and messaging including: Numeric values only10 digit maxFocus away from field without entering a 10 digit phone number displays error state per the design with an error message to enter 10-digit phone number including area code with no hyphens or spaces.\n", "e596_5,596,Verify postal code field handles input of exactly 5 numeric characters.,{ 'postal_code': '12345' },The system accepts the input without any error message.,Given: The User has accessed the Sensor Fell Off/Error message form page When: The User interacts with the mandatory Postal code fieldThen: The system should show the validation and messaging including: Limit input to 5 numeric characters including the space (cannot type beyond the 5th character)Validation message requesting to enter a valid postal code.\n", "e602_9,602,Verify email field handles input of maximum length with valid format.,{ 'email': 'a'.repeat(64) + '@example.com' },The system accepts the input without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory email fieldThen: The system should show the validation and messaging including: A valid format of emailFocus away from field without entering a valid email displays error state per the design with message requesting user to enter a valid email address.\n", "e603_10,603,Verify 'How many days were you wearing the sensor?' field handles selection of maximum days (15 days for FSL 2 Plus and FSL 3 Plus).,{ 'days_wearing_sensor': '15' },The system accepts the input and proceeds without any error message.,Given: The User has accessed the sensor error OR sensor fell off replacement form page When: The User interacts with the mandatory How many days were you wearing the sensor? fieldThen: The system should show the options (based on the product chosen): I'm not sureSame day1 day2 days3 days…14 days15 days (Applicable for FSL 2 Plus and FSL 3 Plus only)\n"]}], "source": ["print(test_cases_fb)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["#### Test Case correction Agent\n", "# Pydantic for structured output\n", "class CorrectedTestCase(BaseModel):\n", "    test_case_id: str = Field(description=\"test case Id from the feedback\")\n", "    test_case_description: str = Field(description=\"Test case that validates the requirement and the feedback\")\n", "    inputs: str = Field(description=\"List of inputs for the test case\")\n", "    expected_outcome: str = Field(description=\"Expected result of the test case\")\n", "\n", "class CorrectedTestCases(BaseModel):\n", "    test_cases: List[CorrectedTestCase] = Field(description=\"A list of the test cases\")\n", "\n", "correction_prompt_template = \"\"\"\n", "You are an LLM agent responsible for correcting test cases for a project based on feedback provided. Each piece of feedback identifies specific issues within a test case, such as missing details, inaccuracies, or irrelevant information. Your task is to review each piece of feedback, locate the corresponding test case, and make the necessary corrections to ensure accuracy, completeness, and relevance.\n", "\n", "Follow these guidelines:\n", "1. For each test case with feedback, carefully read the issue described in the feedback.\n", "2. Modify the test case to address the feedback. This may involve:\n", "    - Adding missing details to make the test case comprehensive.\n", "    - Correcting inaccuracies in inputs, descriptions, or expected outcomes.\n", "    - Removing irrelevant or misleading information.\n", "3. Ensure that each corrected test case maintains the structure and format of:\n", "   - Test Case ID: [ID from feedback]\n", "   - Test Case Description: [Revised description that clearly aligns with the requirement]\n", "   - Inputs: [Updated inputs for the test case]\n", "   - Expected Outcome: [Updated expected outcome for the test case]\n", "\n", "Example format for corrected test cases:\n", "Test Case ID: p300_1\n", "Test Case Description: \"Verify successful login with correct username and password, including case sensitivity.\"\n", "Inputs: {{\"username\": \"validUser\", \"password\": \"ValidPass123\"}}\n", "Expected Outcome: \"User is successfully logged in and redirected to the dashboard.\"\n", "\n", "Only provide corrections for test cases where feedback indicates an issue. Do not modify cases that do not require correction.\n", "\n", "Here is the list of test cases:\n", "{test_cases}\n", "\n", "Here is the feedback for the test cases:\n", "{feedback}\n", "\"\"\"\n", "\n", "# Initialize the llm\n", "llm = AzureChatOpenAI(model='gpt-4o', temperature=0)\n", "structured_llm = llm.with_structured_output(CorrectedTestCases)\n", "correction_prompt = PromptTemplate(input_variables=['test_cases', 'feedback'], template=correction_prompt_template)\n", "correction_generator = correction_prompt | structured_llm\n", "\n", "# Test it out\n", "correction_res = correction_generator.invoke({\"test_cases\": test_cases_fb, \"feedback\": fb})"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["[CorrectedTestCase(test_case_id='e534_2', test_case_description=\"Verify sensor serial number field handles maximum length input with valid characters except 'B', 'I', 'O', and 'S', and displays an error message for invalid characters.\", inputs=\"{ 'sensor_serial_number': 'A1C2E3G4H5J' }\", expected_outcome=\"The system accepts the input and displays it in uppercase without any error message. If invalid characters ('B', 'I', 'O', 'S') are entered, the system displays an error message.\"),\n", " CorrectedTestCase(test_case_id='e595_4', test_case_description='Verify phone number field handles input of exactly 10 numeric characters without spaces or hyphens, and displays an error message for input with fewer than 10 digits.', inputs=\"{ 'phone_number': '1234567890' }\", expected_outcome='The system accepts the input without any error message. If fewer than 10 digits are entered, the system displays an error message.'),\n", " CorrectedTestCase(test_case_id='e596_5', test_case_description='Verify postal code field handles input of exactly 5 numeric characters, and displays an error message for input with fewer than 5 characters.', inputs=\"{ 'postal_code': '12345' }\", expected_outcome='The system accepts the input without any error message. If fewer than 5 characters are entered, the system displays an error message.'),\n", " CorrectedTestCase(test_case_id='e602_9', test_case_description='Verify email field handles input of maximum length with valid format, and displays an error message for invalid email formats.', inputs=\"{ 'email': 'a'.repeat(64) + '@example.com' }\", expected_outcome='The system accepts the input without any error message. If the email format is invalid, the system displays an error message.'),\n", " CorrectedTestCase(test_case_id='e603_10', test_case_description=\"Verify 'How many days were you wearing the sensor?' field handles selection of maximum days (15 days for FSL 2 Plus and FSL 3 Plus), and displays appropriate options for other products.\", inputs=\"{ 'days_wearing_sensor': '15' }\", expected_outcome='The system accepts the input and proceeds without any error message. For other products, the system displays appropriate options based on the product chosen.')]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["correction_res.test_cases"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["def replace_test_cases(corrected_cases, csv_file):\n", "    # Load the CSV file into a DataFrame\n", "    df = pd.read_csv(csv_file)\n", "    \n", "    # Convert corrected cases into a DataFrame\n", "    corrected_df = pd.DataFrame([case.__dict__ for case in corrected_cases])\n", "    \n", "    # Iterate through each row in corrected_df to update matching rows in df\n", "    for _, row in corrected_df.iterrows():\n", "        test_case_id = row['test_case_id']\n", "        \n", "        # Check if the test_case_id exists in the CSV DataFrame\n", "        if test_case_id in df['test_case_id'].values:\n", "            # Locate the index of the matching row in the original DataFrame\n", "            idx = df[df['test_case_id'] == test_case_id].index[0]\n", "            \n", "            # Update only the columns present in corrected_df for the matched row\n", "            for col in corrected_df.columns:\n", "                df.at[idx, col] = row[col]\n", "    \n", "    # Save the updated DataFrame back to the CSV file\n", "    df.to_csv(csv_file, index=False)\n", "\n", "# Replace test cases with corrected data\n", "replace_test_cases(corrected_cases=correction_res.test_cases, csv_file=\"./test_cases/edge_test_cases.csv\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Graph"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["class GraphState(TypedDict):\n", "    csv_file_path: str\n", "    output_file_path: str\n", "    csv_file_contents: str\n", "    pos_csv_file_path: str\n", "    neg_csv_file_path: str\n", "    edge_csv_file_path: str\n", "    pos_fb: str\n", "    neg_fb: str\n", "    edge_fb: str\n", "    pos_test_cases_fb: str\n", "    neg_test_cases_fb: str\n", "    edge_test_cases_fb: str\n", "    output_file_path: str"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["### Nodes\n", "def load_csv(state):\n", "    print(\"---LOADING REQUIREMENTS CSV FILE---\")\n", "    csv_file_path = state['csv_file_path']\n", "    # loader = CSVLoader(file_path=csv_file_path)\n", "    # data = loader.load()    \n", "    # csv_file_contents = \"\"\n", "    # for d in data:\n", "    #     csv_file_contents += d.page_content + \"\\n\\n\"\n", "    csv_file_contents = get_csv_contents(file_path=csv_file_path)\n", "    \n", "    return {\"csv_file_contents\": csv_file_contents}\n", "\n", "def generate_pos_test_cases(state):\n", "    print(\"---GENERATING POSITIVE TEST CASES---\")\n", "    csv_file_contents = state['csv_file_contents']\n", "    res = pos_generator.invoke({\"csv_file\": csv_file_contents})\n", "    # Generate the positive test case csv file \n", "    generate_test_case_csv(test_cases=res.test_cases, prefix='p', output_file='./test_cases/positive_test_cases.csv')\n", "    return {'pos_csv_file_path': './test_cases/positive_test_cases.csv'}\n", "\n", "def generate_neg_test_cases(state):\n", "    print(\"---GENERATING NEGATIVE TEST CASES---\")\n", "    csv_file_contents = state['csv_file_contents']\n", "    neg_res = neg_generator.invoke({\"csv_file\": csv_file_contents})\n", "    # Generate the negative test case csv file\n", "    generate_test_case_csv(test_cases=neg_res.test_cases, prefix='n', output_file='./test_cases/negative_test_cases.csv')\n", "    return {'neg_csv_file_path': './test_cases/negative_test_cases.csv'}\n", "\n", "def generate_edge_test_cases(state):\n", "    print(\"---GENERATING EDGE TEST CASES---\")\n", "    csv_file_contents = state['csv_file_contents']\n", "    edge_res = edge_generator.invoke({\"csv_file\": csv_file_contents})\n", "    # Generate the edge test case csv file\n", "    generate_test_case_csv(test_cases=edge_res.test_cases, prefix='e', output_file='./test_cases/edge_test_cases.csv')\n", "    return {\"edge_csv_file_path\": './test_cases/edge_test_cases.csv'}\n", "\n", "def get_pos_feedback(state):\n", "    print(\"---GENERATING FEEDBACK FOR POSITIVE TEST CASES---\")  \n", "    pos_csv_file_path = state['pos_csv_file_path']\n", "    csv_string = csv_to_string(file_path = pos_csv_file_path)\n", "    feedback_res = feedback_generator.invoke({\"test_case_type\": \"positive\", \"generated_test_cases\": csv_string})\n", "    fb = get_feedback_str(feedback_lst=feedback_res.feedback_list)\n", "    test_cases_fb = get_filtered_generated_test_cases(pos_csv_file_path, feedback_res) \n", "    return {\"pos_fb\": fb, \"pos_test_cases_fb\": test_cases_fb}\n", "\n", "def get_neg_feedback(state):\n", "    print(\"---GENERATING FEEDBACK FOR NEGATIVE TEST CASES---\") \n", "    neg_csv_file_path = state['neg_csv_file_path']\n", "    csv_string = csv_to_string(file_path = neg_csv_file_path)\n", "    feedback_res = feedback_generator.invoke({\"test_case_type\": \"negative\", \"generated_test_cases\": csv_string})\n", "    fb = get_feedback_str(feedback_lst=feedback_res.feedback_list)\n", "    test_cases_fb = get_filtered_generated_test_cases(neg_csv_file_path, feedback_res) \n", "    return {\"neg_fb\": fb, \"neg_test_cases_fb\": test_cases_fb}\n", "\n", "def get_edge_feedback(state):\n", "    print(\"---GENERATING FEEDBACK FOR EDGE TEST CASES---\") \n", "    edge_csv_file_path = state['edge_csv_file_path']\n", "    csv_string = csv_to_string(file_path = edge_csv_file_path)\n", "    feedback_res = feedback_generator.invoke({\"test_case_type\": \"negative\", \"generated_test_cases\": csv_string})\n", "    fb = get_feedback_str(feedback_lst=feedback_res.feedback_list)\n", "    test_cases_fb = get_filtered_generated_test_cases(edge_csv_file_path, feedback_res) \n", "    return {\"edge_fb\": fb, \"edge_test_cases_fb\": test_cases_fb}\n", "    \n", "def correct_pos_tests(state):\n", "    print(\"---CORRECTING POSITIVE TEST CASES BASED ON FEEDBACK---\")\n", "    pos_csv_file_path = state['pos_csv_file_path']\n", "    fb = state['pos_fb']\n", "    test_cases_fb = state['pos_test_cases_fb']\n", "    correction_res = correction_generator.invoke({\"test_cases\": test_cases_fb, \"feedback\": fb})\n", "    replace_test_cases(corrected_cases=correction_res.test_cases, csv_file=pos_csv_file_path)\n", "\n", "def correct_neg_tests(state):\n", "    print(\"---CORRECTING NEGATIVE TEST CASES BASED ON FEEDBACK---\")\n", "    neg_csv_file_path = state['neg_csv_file_path']\n", "    fb = state['neg_fb']\n", "    test_cases_fb = state['neg_test_cases_fb']\n", "    correction_res = correction_generator.invoke({\"test_cases\": test_cases_fb, \"feedback\": fb})\n", "    replace_test_cases(corrected_cases=correction_res.test_cases, csv_file=neg_csv_file_path)\n", "\n", "def correct_edge_tests(state):\n", "    print(\"---CORRECTING EDGE TEST CASES BASED ON FEEDBACK---\")\n", "    edge_csv_file_path = state['edge_csv_file_path']\n", "    fb = state['edge_fb']\n", "    test_cases_fb = state['edge_test_cases_fb']\n", "    correction_res = correction_generator.invoke({\"test_cases\": test_cases_fb, \"feedback\": fb})\n", "    replace_test_cases(corrected_cases=correction_res.test_cases, csv_file=edge_csv_file_path)\n", "    \n", "def combine_csv_files(state):\n", "    print(\"---COMBINING THE CSV FILES---\")\n", "    # Read each CSV file into a DataFrame\n", "    pos_csv_file_path = state['pos_csv_file_path']\n", "    neg_csv_file_path = state['neg_csv_file_path']\n", "    edge_csv_file_path = state['edge_csv_file_path']\n", "    df1 = pd.read_csv(pos_csv_file_path)\n", "    df2 = pd.read_csv(neg_csv_file_path)\n", "    df3 = pd.read_csv(edge_csv_file_path)\n", "    \n", "    # Concatenate the DataFrames\n", "    combined_df = pd.concat([df1, df2, df3], ignore_index=True)\n", "    \n", "    # Get the output file path\n", "    output_file_path = state['output_file_path']\n", "\n", "    # Save the combined DataFrame to a new CSV file\n", "    combined_df.to_csv(output_file_path, index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Build the Graph"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph, START\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"load_csv\", load_csv)\n", "workflow.add_node(\"generate_pos_test_cases\", generate_pos_test_cases)\n", "workflow.add_node(\"generate_neg_test_cases\", generate_neg_test_cases)\n", "workflow.add_node(\"generate_edge_test_cases\", generate_edge_test_cases)\n", "workflow.add_node(\"get_pos_feedback\", get_pos_feedback)\n", "workflow.add_node(\"get_neg_feedback\", get_neg_feedback)\n", "workflow.add_node(\"get_edge_feedback\", get_edge_feedback)\n", "workflow.add_node(\"correct_pos_tests\", correct_pos_tests)\n", "workflow.add_node(\"correct_neg_tests\", correct_neg_tests)\n", "workflow.add_node(\"correct_edge_tests\", correct_edge_tests)\n", "workflow.add_node(\"combine_csv_files\", combine_csv_files)\n", "\n", "# Build the graph\n", "workflow.add_edge(START, \"load_csv\")\n", "workflow.add_edge(\"load_csv\", \"generate_pos_test_cases\")\n", "workflow.add_edge(\"load_csv\", \"generate_neg_test_cases\")\n", "workflow.add_edge(\"load_csv\", \"generate_edge_test_cases\")\n", "workflow.add_edge(\"generate_pos_test_cases\", \"get_pos_feedback\")\n", "workflow.add_edge(\"generate_neg_test_cases\", \"get_neg_feedback\")\n", "workflow.add_edge(\"generate_edge_test_cases\", \"get_edge_feedback\")\n", "workflow.add_edge(\"get_pos_feedback\", \"correct_pos_tests\")\n", "workflow.add_edge(\"get_neg_feedback\", \"correct_neg_tests\")\n", "workflow.add_edge(\"get_edge_feedback\", \"correct_edge_tests\")\n", "workflow.add_edge('correct_pos_tests', 'combine_csv_files')\n", "workflow.add_edge('correct_neg_tests', 'combine_csv_files')\n", "workflow.add_edge('correct_edge_tests', 'combine_csv_files')\n", "workflow.add_edge('combine_csv_files', END)\n", "app = workflow.compile()"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["Graph(nodes={'__start__': Node(id='__start__', name='__start__', data=<class 'langchain_core.utils.pydantic.LangGraphInput'>, metadata=None), 'load_csv': Node(id='load_csv', name='load_csv', data=load_csv(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'generate_pos_test_cases': Node(id='generate_pos_test_cases', name='generate_pos_test_cases', data=generate_pos_test_cases(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'generate_neg_test_cases': Node(id='generate_neg_test_cases', name='generate_neg_test_cases', data=generate_neg_test_cases(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'generate_edge_test_cases': Node(id='generate_edge_test_cases', name='generate_edge_test_cases', data=generate_edge_test_cases(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'get_pos_feedback': Node(id='get_pos_feedback', name='get_pos_feedback', data=get_pos_feedback(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'get_neg_feedback': Node(id='get_neg_feedback', name='get_neg_feedback', data=get_neg_feedback(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'get_edge_feedback': Node(id='get_edge_feedback', name='get_edge_feedback', data=get_edge_feedback(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'correct_pos_tests': Node(id='correct_pos_tests', name='correct_pos_tests', data=correct_pos_tests(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'correct_neg_tests': Node(id='correct_neg_tests', name='correct_neg_tests', data=correct_neg_tests(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'correct_edge_tests': Node(id='correct_edge_tests', name='correct_edge_tests', data=correct_edge_tests(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), 'combine_csv_files': Node(id='combine_csv_files', name='combine_csv_files', data=combine_csv_files(tags=None, recurse=True, func_accepts_config=False, func_accepts={'writer': False, 'store': False}), metadata=None), '__end__': Node(id='__end__', name='__end__', data=<class 'langchain_core.utils.pydantic.LangGraphOutput'>, metadata=None)}, edges=[Edge(source='__start__', target='load_csv', data=None, conditional=False), Edge(source='combine_csv_files', target='__end__', data=None, conditional=False), Edge(source='correct_edge_tests', target='combine_csv_files', data=None, conditional=False), Edge(source='correct_neg_tests', target='combine_csv_files', data=None, conditional=False), Edge(source='correct_pos_tests', target='combine_csv_files', data=None, conditional=False), Edge(source='generate_edge_test_cases', target='get_edge_feedback', data=None, conditional=False), Edge(source='generate_neg_test_cases', target='get_neg_feedback', data=None, conditional=False), Edge(source='generate_pos_test_cases', target='get_pos_feedback', data=None, conditional=False), Edge(source='get_edge_feedback', target='correct_edge_tests', data=None, conditional=False), Edge(source='get_neg_feedback', target='correct_neg_tests', data=None, conditional=False), Edge(source='get_pos_feedback', target='correct_pos_tests', data=None, conditional=False), Edge(source='load_csv', target='generate_edge_test_cases', data=None, conditional=False), Edge(source='load_csv', target='generate_neg_test_cases', data=None, conditional=False), Edge(source='load_csv', target='generate_pos_test_cases', data=None, conditional=False)])"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["app.get_graph()"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---LOADING REQUIREMENTS CSV FILE---\n", "\"Node 'load_csv':\"\n", "'\\n---\\n'\n", "---GENERATING POSITIVE TEST CASES---\n", "---GENERATING NEGATIVE TEST CASES---\n", "---GENERATING EDGE TEST CASES---\n", "CSV file './test_cases/edge_test_cases.csv' has been created successfully.\n", "\"Node 'generate_edge_test_cases':\"\n", "'\\n---\\n'\n", "CSV file './test_cases/negative_test_cases.csv' has been created successfully.\n", "\"Node 'generate_neg_test_cases':\"\n", "'\\n---\\n'\n", "CSV file './test_cases/positive_test_cases.csv' has been created successfully.\n", "\"Node 'generate_pos_test_cases':\"\n", "'\\n---\\n'\n", "---GENERATING FEEDBACK FOR POSITIVE TEST CASES---\n", "---GENERATING FEED<PERSON>CK FOR <PERSON>GATIVE TEST CASES---\n", "---GENERATING FEEDBACK FOR EDGE TEST CASES---\n", "\"Node 'get_edge_feedback':\"\n", "'\\n---\\n'\n", "\"Node 'get_neg_feedback':\"\n", "'\\n---\\n'\n", "\"Node 'get_pos_feedback':\"\n", "'\\n---\\n'\n", "---CORRECTING POSITIVE TEST CASES BASED ON FEEDBACK---\n", "---CORRECTING NEGATIVE TEST CASES BASED ON FEEDBACK---\n", "---CORRECTING <PERSON>D<PERSON> TEST CASES BASED ON FEEDBACK---\n", "\"Node 'correct_neg_tests':\"\n", "'\\n---\\n'\n", "\"Node 'correct_pos_tests':\"\n", "'\\n---\\n'\n", "\"Node 'correct_edge_tests':\"\n", "'\\n---\\n'\n", "---COMBINING THE CSV FILES---\n", "\"Node 'combine_csv_files':\"\n", "'\\n---\\n'\n"]}], "source": ["inputs = {\"csv_file_path\": \"./requirements.csv\", 'output_file_path': './test_cases.csv'}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        # Node\n", "        pprint(f\"Node '{key}':\")\n", "    pprint(\"\\n---\\n\")"]}], "metadata": {"kernelspec": {"display_name": "cap", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 2}