# Imports
from dotenv import load_dotenv
import time
import os
import json
from langchain_openai import AzureOpenAIEmbeddings
from os import listdir
from os.path import isfile, join

# Load the .env file
load_dotenv()
os.environ['AZURE_OPENAI_ENDPOINT'] = os.getenv("AZURE_OPENAI_EMBEDDING_ENDPOINT")
os.environ['OPENAI_API_VERSION'] = '2023-05-15'
embeddings = AzureOpenAIEmbeddings(model='text-embedding-3-small')

class Document:
    def __init__(self, id, page_content, metadata):
        self.id = id
        self.page_content = page_content
        self.metadata = metadata

# Set up exponential backoff parameters
def retry_with_backoff(api_call, *args, max_retries=5, initial_wait=8, **kwargs):
    retries = 0
    wait_time = initial_wait
    while retries < max_retries:
        try:
            return api_call(*args, **kwargs)
        except Exception as e:
            print(f"Rate limit exceeded. Retrying in {wait_time} seconds...")
            time.sleep(wait_time)
            retries += 1
            wait_time *= 2  # Exponential backoff
            
    raise Exception("Max retries exceeded")

def embed_and_store_text_chunks(file_paths, output_dir='./embedding_files'):
    embedding_processed_files = "embedding_processed_files.txt"

    if not os.path.exists(embedding_processed_files):
        with open(embedding_processed_files, 'w') as file:
            pass

    with open(embedding_processed_files, "r") as file:
        embedding_processed_file_paths = file.read()

    os.makedirs(output_dir, exist_ok=True)

    for file_path in file_paths:
        if file_path in embedding_processed_file_paths:
            continue
        
        print(f"Processing File: {file_path}")
        with open(file_path, "r", encoding='utf-8') as f:
            text = f.read()

        # Split text into chunks
        split_text = text.split('page_content')
        split_text = [part.strip()[2:-1] for part in split_text[1:]]

        cleaned_path = os.path.basename(file_path).replace(".txt", "")

        # Convert to Document formatted chunks
        chunk_docs = [Document(id=f"{cleaned_path}_{i}", page_content=chunk, metadata={"source": cleaned_path}) for i, chunk in enumerate(split_text)]

        # Embed each chunk with exponential backoff
        _embeddings = []
        for chunk in split_text:
            embedding_result = retry_with_backoff(lambda: embeddings.embed_documents([chunk]))
            _embeddings.extend(embedding_result)

        # Save the chunks and embeddings as separate JSON files
        chunks_output_path = os.path.join(output_dir, f"{cleaned_path}_chunks.json")
        embeddings_output_path = os.path.join(output_dir, f"{cleaned_path}_embeddings.json")

        with open(chunks_output_path, 'w', encoding='utf-8') as chunk_file:
            json.dump([doc.__dict__ for doc in chunk_docs], chunk_file, indent=4)

        with open(embeddings_output_path, 'w', encoding='utf-8') as embedding_file:
            json.dump(_embeddings, embedding_file, indent=4)

        with open(embedding_processed_files, 'a', encoding='utf-8') as file:
            file.write(f"{file_path}\n")

        print(f"Processed and saved: {file_path}")

file_paths = []
for f in listdir("./contextualized_chunks_files"):
   if isfile(join("./contextualized_chunks_files", f)):
        file_paths.append(join("./contextualized_chunks_files", f))

if __name__ == '__main__': 
    embed_and_store_text_chunks(file_paths)