from pprint import pprint
from graph import create_graph_workflow, create_update_graph_workflow

def generate_test_cases(input_filepath, output_filepath, print_res=True):
    """
    Generates test cases from a requirements CSV file.

    Args:
        input_filepath (str): Path to the input requirements CSV file.
        output_filepath (str): Path to save the generated test cases CSV file.
        print_res (bool): Whether to print the progress of the workflow. Defaults to True.

    Returns:
        None
    """
    # Create the app
    generator = create_graph_workflow()
    # Define the inputs  
    inputs = {"csv_file_path": input_filepath, 'output_file_path': output_filepath}
    if print_res == True:
        for output in generator.stream(inputs):
            for key, value in output.items():
                # Node
                pprint(f"Node '{key}':")
            pprint("\n---\n")
    else:
        for output in generator.stream(inputs):
            for key, value in output.items():
                pass

def update_test_cases(old_csv_filepath, new_csv_filepath, old_test_cases_filepath, output_filepath):
    """
    Updates test cases based on changes in requirements.

    Args:
        old_csv_filepath (str): Path to the old requirements CSV file.
        new_csv_filepath (str): Path to the updated requirements CSV file.
        old_test_cases_filepath (str): Path to the existing test cases CSV file.
        output_filepath (str): Path to save the updated test cases CSV file.

    Returns:
        None
    """
    inputs = {"old_csv_file_path": old_csv_filepath, 
          'new_csv_file_path': new_csv_filepath,
          'old_test_cases_file_path': old_test_cases_filepath,
          'output_file_path': output_filepath, 
          'type_gen': 'general'}
    
    update_app = create_update_graph_workflow()

    for output in update_app.stream(inputs):
        for key, value in output.items():
            # Node
            pprint(f"Node '{key}':")
        pprint("\n---\n")
    pprint(value['output_file_path'])
