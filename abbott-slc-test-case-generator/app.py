from flask import Flask, request, render_template, send_file
import os
from test_case_generator import generate_test_cases, update_test_cases
import traceback
import time

app = Flask(__name__)

UPLOAD_FOLDER = './'
OUTPUT_FOLDER = './'
TEST_CASES_FOLDER = './test_cases'
UDPATE_FOLDER = './update'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)
os.makedirs(TEST_CASES_FOLDER, exist_ok=True)
os.makedirs(UDPATE_FOLDER, exist_ok=True)

@app.route('/')
def home():
    """
    Renders the home page of the web application.

    Returns:
        str: HTML content for the index page.
    """
    return render_template('index.html')

@app.route('/generate_test_cases', methods=['POST'])
def generate_test_cases_endpoint():
    """
    Endpoint to generate test cases from a requirements file.

    Uploads a requirements file, processes it using the `generate_test_cases` function, 
    and returns the generated test cases as a downloadable CSV file.

    Returns:
        flask.Response: The generated test cases file as an attachment or an error message.
    """
    if 'file' not in request.files:
        return "No file uploaded", 400

    requirements_file = request.files['file']
    if requirements_file.filename == '':
        return "No file selected", 400
    
    try:
        filepath = os.path.join(UPLOAD_FOLDER, requirements_file.filename)
        requirements_file.save(filepath)

        # Generate test cases
        timestamp = time.strftime("%Y%m%d%H%M%S")
        output_filepath = os.path.join(OUTPUT_FOLDER, "".join(['test_cases_', timestamp,'.csv']))
        generate_test_cases(filepath, output_filepath)

        return send_file(output_filepath, as_attachment=True)
    except Exception as e:
        traceback.print_exc()
        return str(e), 500
    
@app.route('/update_test_cases', methods=['POST'])
def update_test_cases_endpoint():
    """
    Endpoint to update test cases based on changes in requirements.

    Uploads the old requirements file, new requirements file, and old test cases file,
    processes them using the `update_test_cases` function, and returns the updated 
    test cases as a downloadable CSV file.

    Returns:
        flask.Response: The updated test cases file as an attachment or an error message.
    """
    required_files = ['old_req_file', 'new_req_file', 'old_test_cases_file']

    if not any(rf in request.files for rf in required_files):
        return "Not all required files are uploaded for update process", 400

    if any(request.files[rf] == '' for rf in required_files):
        return "Not all required files are selected for update process", 400
    
    try:
        old_req_file = request.files['old_req_file']
        new_req_file = request.files['new_req_file']
        old_test_cases_file = request.files['old_test_cases_file']

        old_csv_filepath = os.path.join(UDPATE_FOLDER, old_req_file.filename)
        old_req_file.save(old_csv_filepath)

        new_csv_filepath = os.path.join(UDPATE_FOLDER, new_req_file.filename)
        new_req_file.save(new_csv_filepath)

        old_test_cases_filepath = os.path.join(UDPATE_FOLDER, old_test_cases_file.filename)
        old_test_cases_file.save(old_test_cases_filepath)

        # update test cases
        timestamp = time.strftime("%Y%m%d%H%M%S")
        output_filepath = os.path.join(UDPATE_FOLDER, "".join(['updated_test_cases_', timestamp,'.csv']))
        update_test_cases(old_csv_filepath, new_csv_filepath, old_test_cases_filepath, output_filepath)

        return send_file(output_filepath, as_attachment=True)
    except Exception as e:
        traceback.print_exc()
        return str(e), 500

if __name__ == '__main__':
    """
    Runs the Flask application in debug mode.

    The application provides endpoints to generate and update test cases.
    """
    app.run(debug=True)