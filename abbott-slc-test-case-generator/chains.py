# Imports
from langchain_core.prompts import PromptTemplate
from langchain_openai import AzureChatOpenAI
from dotenv import load_dotenv
from data_models import * 

load_dotenv()

def get_pos_generator():
    """
    Generates positive test cases for requirements in a CSV file.

    The positive test cases validate the correct and expected functionality 
    of each requirement provided in the CSV file. This function creates a prompt 
    template for generating detailed test cases and initializes an LLM to process the requirements.

    Returns:
        Callable: A pipeline combining the prompt template and the LLM for generating positive test cases.
    """
    pos_tst_prompt_template = """
    You are an LLM agent responsible for generating positive test cases for a project based on the requirements provided in a CSV file. The CSV file contains each requirement in a structured format. Your task is to analyze each requirement and produce positive test cases only where applicable, confirming the correct and expected functionality of the project for each relevant requirement.

    Follow these guidelines:
    1. Carefully read each requirement from the CSV file.
    2. Generate one or more positive test cases per requirement if possible. Positive test cases should validate that the feature or function works as intended and meets the requirement.
    3. Each test case should be detailed, specifying inputs, expected outcomes, and any relevant setup.
    4. Skip any requirements for which a positive test case is not applicable. Avoid generating negative test cases or edge cases, as these are out of scope for this task.

    Format each test case in this structure:
    - Requirement ID: [ID from CSV]
    - Test Case Description: [Test case that validates the requirement]
    - Inputs: [List of inputs for the test case]
    - Expected Outcome: [Expected result of the test case]

    Example:
    Requirement ID: 001
    Test Case Description: "Verify successful login with correct username and password."
    Inputs: {{"username": "validUser", "password": "validPass123"}}
    Expected Outcome: "User is successfully logged in and redirected to the dashboard."

    Generate a complete list of positive test cases only for requirements that allow for positive test cases.
    Here is the CSV file contents: 
    {csv_file}
    """
    pos_tst_prompt = PromptTemplate(input_variables=['csv_file'], template=pos_tst_prompt_template)

    # Initialize the llm
    llm = AzureChatOpenAI(model='gpt-4o', temperature=0)
    structured_llm = llm.with_structured_output(TestCases)
    pos_generator = pos_tst_prompt | structured_llm

    return pos_generator

def get_neg_generator():
    """
    Generates negative test cases for requirements in a CSV file.

    The negative test cases validate how the system handles errors, invalid inputs, 
    or unexpected conditions for each requirement provided in the CSV file. This function 
    creates a prompt template for generating test cases and initializes an LLM for processing.

    Returns:
        Callable: A pipeline combining the prompt template and the LLM for generating negative test cases.
    """
    neg_tst_prompt_template = """
    You are an LLM agent responsible for generating negative test cases for a project based on the requirements provided in a CSV file. The CSV file contains each requirement in a structured format. Your task is to analyze each requirement and produce negative test cases only where applicable, confirming that the system handles errors, invalid inputs, and unexpected conditions appropriately for each relevant requirement.

    Follow these guidelines:
    1. Carefully read each requirement from the CSV file.
    2. Generate one or more negative test cases per requirement if possible. Negative test cases should validate that the feature or function appropriately handles invalid inputs, errors, or unexpected conditions, aligning with the requirement.
    3. Each test case should be detailed, specifying invalid inputs, expected outcomes, and any relevant setup.
    4. Skip any requirements for which a negative test case is not applicable. Avoid generating positive test cases, as these are out of scope for this task.

    Format each test case in this structure:
    - Requirement ID: [ID from CSV]
    - Test Case Description: [Test case that validates handling of invalid inputs or errors]
    - Inputs: [List of invalid inputs for the test case]
    - Expected Outcome: [Expected error message or result of the test case]

    Example:
    Requirement ID: 001
    Test Case Description: "Verify login fails with incorrect username and password."
    Inputs: {{"username": "invalidUser", "password": "wrongPass123"}}
    Expected Outcome: "User receives an error message indicating invalid login credentials."

    Generate a complete list of negative test cases only for requirements that allow for negative test cases.
    Here is the CSV file contents: 
    {csv_file}
    """
    # Initialize the llm
    llm = AzureChatOpenAI(model='gpt-4o', temperature=0)
    structured_llm = llm.with_structured_output(TestCases)
    neg_tst_prompt = PromptTemplate(input_variables=['csv_file'], template=neg_tst_prompt_template)
    neg_generator = neg_tst_prompt | structured_llm

    return neg_generator

def get_edge_generator():
    """
    Generates edge test cases for requirements in a CSV file.

    The edge test cases validate the system's behavior under boundary conditions, extreme values, 
    or rare scenarios for each requirement provided in the CSV file. This function creates a 
    prompt template for generating edge test cases and initializes an LLM to process the requirements.

    Returns:
        Callable: A pipeline combining the prompt template and the LLM for generating edge test cases.
    """
    edge_tst_prompt_template = """
    You are an LLM agent responsible for generating edge case test scenarios for a project based on the requirements provided in a CSV file. The CSV file contains each requirement in a structured format. Your task is to analyze each requirement and produce edge cases only where applicable, confirming that the system can handle boundary conditions, extreme values, and atypical scenarios appropriately for each relevant requirement.

    Follow these guidelines:
    1. Carefully read each requirement from the CSV file.
    2. Generate one or more edge cases per requirement if possible. Edge cases should validate that the feature or function works as expected under boundary conditions, extreme values, or rare scenarios, in alignment with the requirement.
    3. Each test case should be detailed, specifying edge-case inputs, expected outcomes, and any relevant setup.
    4. Skip any requirements for which an edge case is not applicable. Avoid generating standard positive or negative test cases, as these are out of scope for this task.

    Format each test case in this structure:
    - Requirement ID: [ID from CSV]
    - Test Case Description: [Test case that validates handling of edge-case scenarios]
    - Inputs: [List of edge-case inputs for the test case]
    - Expected Outcome: [Expected result of the test case]

    Example:
    Requirement ID: 001
    Test Case Description: "Verify login handles maximum allowed length for username and password."
    Inputs: {{"username": "user_" * 20, "password": "pass_" * 20}}
    Expected Outcome: "User is successfully logged in if credentials are valid, or receives an appropriate error message if invalid."

    Generate a complete list of edge cases only for requirements that allow for edge cases.
    Here is the CSV file contents: 
    {csv_file}
    """
    # Initialize the llm
    llm = AzureChatOpenAI(model='gpt-4o', temperature=0)
    structured_llm = llm.with_structured_output(TestCases)
    edge_tst_prompt = PromptTemplate(input_variables=['csv_file'], template=edge_tst_prompt_template)
    edge_generator = edge_tst_prompt | structured_llm

    return edge_generator

def get_feedback_generator():
    """
    Generates feedback for test cases generated for a project.

    The feedback is based on analyzing generated test cases and identifying issues such as missing details, 
    inaccuracies, or irrelevant information. The function reviews the test cases and outputs feedback in 
    a structured format.

    Returns:
        Callable: A pipeline combining the prompt template and the LLM for generating test case feedback.
    """
    feedback_prompt_template = """
    You are an LLM agent responsible for reviewing {test_case_type} test cases generated for a project based on specific requirements. Your task is to analyze each test case against the associated requirement and provide feedback only for cases that are incorrect, incomplete, or contain inaccuracies or irrelevant details.

    Follow these guidelines:
    1. Review each requirement and its corresponding test cases.
    2. Identify test cases that need correction due to missing aspects, inaccuracies, or irrelevant information (hallucinations).
    3. For each test case that requires correction, provide concise feedback that specifies:
        - The requirement ID.
        - A brief but clear explanation of the issue in the test case, including missing details, inaccuracies, or suggestions for improvement.

    Format your feedback in the structure of a `Feedback` model:
    - feedback_list: [
        {{
            "test_case_id": "test case ID where an issue is identified",
            "feedback": "Detailed feedback on what is wrong with the test case and suggested modifications."
        }},
        ...
    ]

    Only provide feedback for test cases that need correction; omit feedback for cases that are accurate and complete.

    Example:
    feedback_list: [
        {{
            "test_case_id": "p20_1",
            "feedback": "Test case does not account for server downtime. Suggest adding a scenario to handle service unavailability."
        }},
        {{
            "test_case_id": "e890_4",
            "feedback": "Unnecessary details in the test case that are not relevant to the requirement. Simplify the test to focus on core functionality."
        }}
    ]
    Here are the generated test cases:
    {generated_test_cases}
    """

    # Initialize the llm
    llm = AzureChatOpenAI(model='gpt-4o', temperature=0)
    structured_llm = llm.with_structured_output(Feedback)
    feedback_tst_prompt = PromptTemplate(input_variables=['test_case_type', 'generated_test_cases'], template=feedback_prompt_template)
    feedback_generator = feedback_tst_prompt | structured_llm

    return feedback_generator

def get_correction_generator():
    """
    Generates corrected test cases based on feedback.

    The function takes the test cases and feedback as input and modifies the test cases 
    to address the issues identified in the feedback, ensuring accuracy and relevance.

    Returns:
        Callable: A pipeline combining the prompt template and the LLM for correcting test cases.
    """
    correction_prompt_template = """
    You are an LLM agent responsible for correcting test cases for a project based on feedback provided. Each piece of feedback identifies specific issues within a test case, such as missing details, inaccuracies, or irrelevant information. Your task is to review each piece of feedback, locate the corresponding test case, and make the necessary corrections to ensure accuracy, completeness, and relevance.

    Follow these guidelines:
    1. For each test case with feedback, carefully read the issue described in the feedback.
    2. Modify the test case to address the feedback. This may involve:
        - Adding missing details to make the test case comprehensive.
        - Correcting inaccuracies in inputs, descriptions, or expected outcomes.
        - Removing irrelevant or misleading information.
    3. Ensure that each corrected test case maintains the structure and format of:
    - Test Case ID: [ID from feedback]
    - Test Case Description: [Revised description that clearly aligns with the requirement]
    - Inputs: [Updated inputs for the test case]
    - Expected Outcome: [Updated expected outcome for the test case]

    Example format for corrected test cases:
    Test Case ID: p300_1
    Test Case Description: "Verify successful login with correct username and password, including case sensitivity."
    Inputs: {{"username": "validUser", "password": "ValidPass123"}}
    Expected Outcome: "User is successfully logged in and redirected to the dashboard."

    Only provide corrections for test cases where feedback indicates an issue. Do not modify cases that do not require correction.

    Here is the list of test cases:
    {test_cases}

    Here is the feedback for the test cases:
    {feedback}
    """

    # Initialize the llm
    llm = AzureChatOpenAI(model='gpt-4o', temperature=0)
    structured_llm = llm.with_structured_output(CorrectedTestCases)
    correction_prompt = PromptTemplate(input_variables=['test_cases', 'feedback'], template=correction_prompt_template)
    correction_generator = correction_prompt | structured_llm
    
    return correction_generator


# -----------------------------------Chains for Update System----------------------------------- #

def get_pos_update_generator():
    """
    Generates new positive test cases for updated requirements.

    The function identifies gaps in the existing test coverage due to updated requirements 
    and generates additional positive test cases to validate the new functionality.

    Returns:
        Callable: A pipeline combining the prompt template and the LLM for generating updated positive test cases.
    """
    pos_update_tst_prompt_template = """
    You are an LLM agent responsible for generating new positive test cases for a project based on updates in requirements. The task involves analyzing the updated requirements and previously generated test cases to produce a list of new positive test cases. Your goal is to ensure that the test cases accurately reflect the updated requirements.

    Follow these guidelines:
    1. Carefully analyze the updated requirements and previously generated test cases.
    2. Identify gaps or changes in the test coverage due to the updated requirements and generate new positive test cases.
    3. Ensure each new test case is detailed, specifying inputs, expected outcomes, and any relevant setup.
    4. Focus solely on positive test cases that validate the correct and expected functionality of the project according to the updated requirements.

    Format each new test case in this structure:
    - Requirement ID: [ID from CSV or reference string]
    - Test Case Description: [Test case that validates the requirement]
    - Inputs: [List of inputs for the test case]
    - Expected Outcome: [Expected result of the test case]

    Example for a new test case:
    Requirement ID: 001
    Test Case Description: "Verify successful login with updated password policy (minimum 12 characters)."
    Inputs: {{"username": "validUser", "password": "ValidPass1234"}}
    Expected Outcome: "User is successfully logged in and redirected to the dashboard."

    Here is the input data for the task:
    - Updated Requirements: {changed_req}
    - Old Test Cases: {old_test_cases}
    """


    pos_tst_prompt = PromptTemplate(input_variables=['changed_req', 'old_test_cases'], template=pos_update_tst_prompt_template)

    # Initialize the llm
    llm = AzureChatOpenAI(model='gpt-4o', temperature=0)
    structured_llm = llm.with_structured_output(TestCases)
    pos_generator = pos_tst_prompt | structured_llm
    return pos_generator

def get_neg_update_generator():
    """
    Generates new negative test cases for updated requirements.

    The function identifies gaps in the existing test coverage due to updated requirements 
    and generates additional negative test cases to validate how the system handles invalid inputs.

    Returns:
        Callable: A pipeline combining the prompt template and the LLM for generating updated negative test cases.
    """
    neg_update_tst_prompt_template = """
    You are an LLM agent responsible for generating new negative test cases for a project based on updates in requirements. The task involves analyzing the updated requirements and previously generated test cases to produce a list of new negative test cases. Your goal is to ensure that the test cases accurately reflect the system's ability to handle invalid or unexpected inputs.

    Follow these guidelines:
    1. Carefully analyze the updated requirements and previously generated test cases.
    2. Identify gaps or changes in the test coverage due to the updated requirements and generate new negative test cases.
    3. Ensure each new test case is detailed, specifying invalid inputs, expected outcomes (such as error messages or proper handling), and any relevant setup.
    4. Focus solely on negative test cases that validate the system's behavior under invalid or unexpected conditions.

    Format each new test case in this structure:
    - Requirement ID: [ID from CSV or reference string]
    - Test Case Description: [Test case that validates the requirement under invalid conditions]
    - Inputs: [List of invalid or unexpected inputs for the test case]
    - Expected Outcome: [Expected result of the test case]

    Example for a new test case:
    Requirement ID: 001
    Test Case Description: "Verify error message when login is attempted with a password shorter than the required minimum length."
    Inputs: {{"username": "validUser", "password": "short"}}
    Expected Outcome: "The system displays an error message: 'Password must be at least 12 characters long.'"

    Here is the input data for the task:
    - Updated Requirements: {changed_req}
    - Old Test Cases: {old_test_cases}
    """

    neg_tst_prompt = PromptTemplate(input_variables=['changed_req', 'old_test_cases'], template=neg_update_tst_prompt_template)

    # Initialize the llm
    llm = AzureChatOpenAI(model='gpt-4o', temperature=0)
    structured_llm = llm.with_structured_output(TestCases)
    neg_generator = neg_tst_prompt | structured_llm
    return neg_generator

def get_edge_update_generator():
    """
    Generates new edge test cases for updated requirements.

    The function identifies gaps in the existing test coverage due to updated requirements 
    and generates additional edge test cases to validate the system's behavior under boundary or extreme conditions.

    Returns:
        Callable: A pipeline combining the prompt template and the LLM for generating updated edge test cases.
    """
    edge_update_tst_prompt_template = """
    You are an LLM agent responsible for generating new edge test cases for a project based on updates in requirements. The task involves analyzing the updated requirements and previously generated test cases to produce a list of new edge test cases. Your goal is to ensure that the test cases accurately reflect the system's behavior under boundary or extreme conditions.

    Follow these guidelines:
    1. Carefully analyze the updated requirements and previously generated test cases.
    2. Identify gaps or changes in the test coverage due to the updated requirements and generate new edge test cases.
    3. Ensure each new test case is detailed, specifying boundary or extreme inputs, expected outcomes, and any relevant setup.
    4. Focus solely on edge test cases that validate the system's behavior under edge conditions.

    Format each new test case in this structure:
    - Requirement ID: [ID from CSV or reference string]
    - Test Case Description: [Test case that validates the requirement under boundary or extreme conditions]
    - Inputs: [List of boundary or extreme inputs for the test case]
    - Expected Outcome: [Expected result of the test case]

    Example for a new test case:
    Requirement ID: 002
    Test Case Description: "Verify system behavior when the username exceeds the maximum allowed length."
    Inputs: {{"username": "a" * 101, "password": "ValidPass1234"}}  # Username exceeds 100 characters
    Expected Outcome: "The system displays an error message: 'Username cannot exceed 100 characters.'"

    Here is the input data for the task:
    - Updated Requirements: {changed_req}
    - Old Test Cases: {old_test_cases}
    """

    edge_tst_prompt = PromptTemplate(input_variables=['changed_req', 'old_test_cases'], template=edge_update_tst_prompt_template)

    # Initialize the llm
    llm = AzureChatOpenAI(model='gpt-4o', temperature=0)
    structured_llm = llm.with_structured_output(TestCases)
    edge_generator = edge_tst_prompt | structured_llm
    return edge_generator

def get_general_generator():
    """
    Generates comprehensive test cases (positive, negative, and edge) for updated requirements.

    The function analyzes updated requirements and previously generated test cases to 
    produce a comprehensive list of new test cases, covering all scenarios.

    Returns:
        Callable: A pipeline combining the prompt template and the LLM for generating comprehensive test cases.
    """
    general_test_case_prompt_template = """
    You are an LLM agent responsible for generating test cases (positive, negative, and edge) for a project based on updates in requirements. The task involves analyzing the updated requirements and previously generated test cases to produce a comprehensive list of test cases. Your goal is to ensure that the test cases accurately reflect the updated requirements.

    Follow these guidelines:
    1. Carefully analyze the updated requirements and previously generated test cases.
    2. Identify gaps or changes in test coverage due to the updated requirements and generate new test cases for the following types:
    - Positive test cases: Validate the correct and expected functionality.
    - Negative test cases: Validate system behavior with invalid or unexpected inputs.
    - Edge test cases: Validate system behavior under boundary or extreme conditions.
    3. Ensure each test case is detailed, specifying inputs, expected outcomes, and any relevant setup.
    4. Clearly specify the test type for each test case (positive, negative, or edge).

    Format each test case in this structure:
    - Requirement ID: [ID from CSV or reference string]
    - Test Case Description: [Test case that validates the requirement]
    - Inputs: [List of inputs for the test case]
    - Expected Outcome: [Expected result of the test case]
    - Test Type: [positive, negative, or edge]

    Example test cases:
    Requirement ID: 001
    Test Case Description: "Verify successful login with correct username and password."
    Inputs: {{"username": "validUser", "password": "validPass123"}}
    Expected Outcome: "User is successfully logged in and redirected to the dashboard."
    Test Type: positive

    Requirement ID: 002
    Test Case Description: "Verify error message when login is attempted with a password shorter than the required minimum length."
    Inputs: {{"username": "validUser", "password": "short"}}
    Expected Outcome: "The system displays an error message: 'Password must be at least 12 characters long.'"
    Test Type: negative

    Requirement ID: 003
    Test Case Description: "Verify system behavior when the username exceeds the maximum allowed length."
    Inputs: {{"username": "a" * 101, "password": "ValidPass1234"}}  # Username exceeds 100 characters
    Expected Outcome: "The system displays an error message: 'Username cannot exceed 100 characters.'"
    Test Type: edge

    Here is the input data for the task:
    - Updated Requirements: {changed_req}
    - Old Test Cases: {old_test_cases}
    """

    general_tst_prompt = PromptTemplate(input_variables=['changed_req', 'old_test_cases'], template=general_test_case_prompt_template)

    # Initialize the llm
    llm = AzureChatOpenAI(model='gpt-4o', temperature=0)
    structured_llm = llm.with_structured_output(GenTestCases)
    general_generator = general_tst_prompt | structured_llm
    return general_generator

