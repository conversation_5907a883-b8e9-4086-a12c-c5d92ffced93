# Imports
from pydantic import BaseModel, Field

# Data Models
class GradeDocuments(BaseModel):
    """
    Represents a binary score model for evaluating the relevance of retrieved documents.

    Attributes:
        binary_score (str): Indicates whether the documents are relevant to the question.
                           Accepted values are 'yes' or 'no'.
    """
    binary_score: str = Field(
        description="Documents are relevant to the question, 'yes' or 'no'"
    )

class GradeHallucinations(BaseModel):
    """
    Represents a binary score model for evaluating the grounding of an answer in retrieved facts.

    Attributes:
        binary_score (str): Indicates whether the answer is grounded in the provided facts.
                           Accepted values are 'yes' or 'no'.
    """

    binary_score: str = Field(
        description="Answer is grounded in the facts, 'yes' or 'no'"
    )

class GradeAnswer(BaseModel):
    """
    Represents a binary score model for assessing whether an answer adequately resolves a question.

    Attributes:
        binary_score (str): Indicates whether the answer addresses the question.
                           Accepted values are 'yes' or 'no'.
    """

    binary_score: str = Field(
        description="Answer addresses the question, 'yes' or 'no'"
    )