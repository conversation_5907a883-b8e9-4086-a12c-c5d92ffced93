document.getElementById('qa-form').addEventListener('submit', async function(event) {
  event.preventDefault();
  
  const question = document.getElementById('question').value;
  const answerElement = document.getElementById('answer');
  const loadingElement = document.getElementById('loading');
  
  // Clear previous answer and show "processing" message
  answerElement.textContent = '';
  loadingElement.style.display = 'block';
  
  try {
    const response = await fetch('/get_answer', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ question: question })
    });
    
    if (response.ok) {
      const data = await response.json();
      answerElement.textContent = data.answer;
    } else {
      answerElement.textContent = 'Error retrieving answer.';
    }
  } catch (error) {
    answerElement.textContent = 'An error occurred: ' + error.message;
  } finally {
    // Hide "processing" message after getting the response
    loadingElement.style.display = 'none';
  }
});