# Imports
from rank_bm25 import BM25Okapi

# LangChain BM25 retriever
class BM25Retriever:
    def __init__(self, chunks):
        self.chunks = chunks
        tokenized_docs = [doc.page_content.split(" ") for doc in chunks]
        self.bm25 = BM25<PERSON><PERSON>pi(tokenized_docs)

    def get_relevant_documents(self, query, k=3):
        query_tokenized = query.split(" ")
        scores = self.bm25.get_scores(query_tokenized)
        ranked_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)
        relevant_docs = [self.chunks[i] for i in ranked_indices]
        scores = [scores[i] for i in ranked_indices]
        return relevant_docs[:k], scores[:k]