# Imports
import os
import pandas as pd
from utils import *
from chains import * 

# Get all of the chains
pos_generator = get_pos_generator()
neg_generator = get_neg_generator()
edge_generator = get_edge_generator()
feedback_generator = get_feedback_generator()
correction_generator = get_correction_generator()

# Get all of the update chains
pos_update_generator = get_pos_update_generator()
neg_update_generator = get_neg_update_generator()
edge_update_generator = get_edge_update_generator()
general_generator = get_general_generator()

### Nodes
def load_csv(state):
    """
    Loads the contents of a requirements CSV file into memory.

    Args:
        state (dict): The current state of the workflow, containing the path to the CSV file.

    Returns:
        dict: Updated state with the contents of the CSV file loaded as a string.
    """
    print("---LOADING REQUIREMENTS CSV FILE---")
    csv_file_path = state['csv_file_path']
    # loader = CSVLoader(file_path=csv_file_path)
    # data = loader.load()    
    # csv_file_contents = ""
    # for d in data:
    #     csv_file_contents += d.page_content + "\n\n"
    csv_file_contents = get_csv_contents(file_path=csv_file_path)
    
    return {"csv_file_contents": csv_file_contents}

def generate_pos_test_cases(state):
    """
    Generates positive test cases from the requirements in the CSV file.

    Args:
        state (dict): The current state of the workflow, containing the path to the requirements CSV and its contents.

    Returns:
        dict: Updated state with the path to the generated positive test cases CSV file.
    """
    print("---GENERATING POSITIVE TEST CASES---")
    csv_file_path = state['csv_file_path']
    csv_file_contents = state['csv_file_contents']
    res = pos_generator.invoke({"csv_file": csv_file_contents})
    # Generate the positive test case csv file 
    generate_test_case_csv(test_cases=res.test_cases, prefix='p', output_file='./test_cases/positive_test_cases.csv', req_desc_file=csv_file_path)
    return {'pos_csv_file_path': './test_cases/positive_test_cases.csv'}

def generate_neg_test_cases(state):
    """
    Generates negative test cases from the requirements in the CSV file.

    Args:
        state (dict): The current state of the workflow, containing the path to the requirements CSV and its contents.

    Returns:
        dict: Updated state with the path to the generated negative test cases CSV file.
    """
    print("---GENERATING NEGATIVE TEST CASES---")
    csv_file_path = state['csv_file_path']
    csv_file_contents = state['csv_file_contents']
    neg_res = neg_generator.invoke({"csv_file": csv_file_contents})
    # Generate the negative test case csv file
    generate_test_case_csv(test_cases=neg_res.test_cases, prefix='n', output_file='./test_cases/negative_test_cases.csv', req_desc_file=csv_file_path)
    return {'neg_csv_file_path': './test_cases/negative_test_cases.csv'}

def generate_edge_test_cases(state):
    """
    Generates edge test cases from the requirements in the CSV file.

    Args:
        state (dict): The current state of the workflow, containing the path to the requirements CSV and its contents.

    Returns:
        dict: Updated state with the path to the generated edge test cases CSV file.
    """
    print("---GENERATING EDGE TEST CASES---")
    csv_file_path = state['csv_file_path']
    csv_file_contents = state['csv_file_contents']
    edge_res = edge_generator.invoke({"csv_file": csv_file_contents})
    # Generate the edge test case csv file
    generate_test_case_csv(test_cases=edge_res.test_cases, prefix='e', output_file='./test_cases/edge_test_cases.csv', req_desc_file=csv_file_path)
    return {"edge_csv_file_path": './test_cases/edge_test_cases.csv'}

def get_pos_feedback(state):
    """
    Generates feedback for positive test cases.

    Args:
        state (dict): The current state of the workflow, containing the path to the positive test cases CSV file.

    Returns:
        dict: Updated state with the feedback and filtered positive test cases.
    """
    print("---GENERATING FEEDBACK FOR POSITIVE TEST CASES---")  
    pos_csv_file_path = state['pos_csv_file_path']
    csv_string = csv_to_string(file_path = pos_csv_file_path)
    feedback_res = feedback_generator.invoke({"test_case_type": "positive", "generated_test_cases": csv_string})
    fb = get_feedback_str(feedback_lst=feedback_res.feedback_list)
    test_cases_fb = get_filtered_generated_test_cases(pos_csv_file_path, feedback_res) 
    return {"pos_fb": fb, "pos_test_cases_fb": test_cases_fb}

def get_neg_feedback(state):
    """
    Generates feedback for negative test cases.

    Args:
        state (dict): The current state of the workflow, containing the path to the negative test cases CSV file.

    Returns:
        dict: Updated state with the feedback and filtered negative test cases.
    """
    print("---GENERATING FEEDBACK FOR NEGATIVE TEST CASES---") 
    neg_csv_file_path = state['neg_csv_file_path']
    csv_string = csv_to_string(file_path = neg_csv_file_path)
    feedback_res = feedback_generator.invoke({"test_case_type": "negative", "generated_test_cases": csv_string})
    fb = get_feedback_str(feedback_lst=feedback_res.feedback_list)
    test_cases_fb = get_filtered_generated_test_cases(neg_csv_file_path, feedback_res) 
    return {"neg_fb": fb, "neg_test_cases_fb": test_cases_fb}

def get_edge_feedback(state):
    """
    Generates feedback for edge test cases.

    Args:
        state (dict): The current state of the workflow, containing the path to the edge test cases CSV file.

    Returns:
        dict: Updated state with the feedback and filtered edge test cases.
    """
    print("---GENERATING FEEDBACK FOR EDGE TEST CASES---") 
    edge_csv_file_path = state['edge_csv_file_path']
    csv_string = csv_to_string(file_path = edge_csv_file_path)
    feedback_res = feedback_generator.invoke({"test_case_type": "negative", "generated_test_cases": csv_string})
    fb = get_feedback_str(feedback_lst=feedback_res.feedback_list)
    test_cases_fb = get_filtered_generated_test_cases(edge_csv_file_path, feedback_res) 
    return {"edge_fb": fb, "edge_test_cases_fb": test_cases_fb}
    
def correct_pos_tests(state):
    """
    Corrects positive test cases based on feedback.

    Args:
        state (dict): The current state of the workflow, containing the positive test cases and feedback.

    Returns:
        None
    """
    print("---CORRECTING POSITIVE TEST CASES BASED ON FEEDBACK---")
    pos_csv_file_path = state['pos_csv_file_path']
    fb = state['pos_fb']
    test_cases_fb = state['pos_test_cases_fb']
    correction_res = correction_generator.invoke({"test_cases": test_cases_fb, "feedback": fb})
    replace_test_cases(corrected_cases=correction_res.test_cases, csv_file=pos_csv_file_path)

def correct_neg_tests(state):
    """
    Corrects negative test cases based on the feedback provided.

    Args:
        state (dict): Contains the path to the negative test cases file (`neg_csv_file_path`),
                      the feedback (`neg_fb`), and the filtered test cases (`neg_test_cases_fb`).

    Returns:
        None
    """
    print("---CORRECTING NEGATIVE TEST CASES BASED ON FEEDBACK---")
    neg_csv_file_path = state['neg_csv_file_path']
    fb = state['neg_fb']
    test_cases_fb = state['neg_test_cases_fb']
    correction_res = correction_generator.invoke({"test_cases": test_cases_fb, "feedback": fb})
    replace_test_cases(corrected_cases=correction_res.test_cases, csv_file=neg_csv_file_path)

def correct_edge_tests(state):
    """
    Corrects edge test cases based on the feedback provided.

    Args:
        state (dict): Contains the path to the edge test cases file (`edge_csv_file_path`),
                      the feedback (`edge_fb`), and the filtered test cases (`edge_test_cases_fb`).

    Returns:
        None
    """
    print("---CORRECTING EDGE TEST CASES BASED ON FEEDBACK---")
    edge_csv_file_path = state['edge_csv_file_path']
    fb = state['edge_fb']
    test_cases_fb = state['edge_test_cases_fb']
    correction_res = correction_generator.invoke({"test_cases": test_cases_fb, "feedback": fb})
    replace_test_cases(corrected_cases=correction_res.test_cases, csv_file=edge_csv_file_path)
    
def combine_csv_files(state):
    """
    Combines positive, negative, and edge test cases into a single CSV file.

    Args:
        state (dict): The current state of the workflow, containing paths to all test case CSV files.

    Returns:
        None
    """
    print("---COMBINING THE CSV FILES---")
    # Read each CSV file into a DataFrame
    pos_csv_file_path = state['pos_csv_file_path']
    neg_csv_file_path = state['neg_csv_file_path']
    edge_csv_file_path = state['edge_csv_file_path']
    df1 = pd.read_csv(pos_csv_file_path)
    df2 = pd.read_csv(neg_csv_file_path)
    df3 = pd.read_csv(edge_csv_file_path)
    
    # Concatenate the DataFrames
    combined_df = pd.concat([df1, df2, df3], ignore_index=True)
    
    # Get the output file path
    output_file_path = state['output_file_path']

    # Save the combined DataFrame to a new CSV file
    combined_df.to_csv(output_file_path, index=False)

# -------------------------------Update System Nodes------------------------------------ #
### Nodes
def check_differences(state):
    """
    Identifies differences between the old and new requirements files.

    Args:
        state (dict): Contains paths to the old (`old_csv_file_path`) and new (`new_csv_file_path`) requirements files.

    Returns:
        dict: Updated state with removed, new, and changed requirements, and placeholders for filtered paths.
    """
    # Set the paths for the updated files as None
    rem_filtered_path = None
    new_test_path = None
    update_test_path = None
    print("---CHECKING DIFFERENCES IN THE FILES---")
    # Get the paths for the old and new file
    old_csv_file_path = state['old_csv_file_path']
    new_csv_file_path = state['new_csv_file_path']

    # Check the removed sections
    removed_sections = get_removed_sections(old_csv_file_path, new_csv_file_path, 
                                            unique_column='Req ID')
    # Get the new sections (requirements that were added)
    new_sections = get_new_sections(old_csv_file_path, new_csv_file_path, 
                                    unique_column='Req ID')
    # Get the requirements that were updated
    changed_rows = get_changed_rows(old_csv_file_path, new_csv_file_path, 
                                    unique_column='Req ID')
    
    return {"removed_sections": removed_sections, "new_sections": new_sections, 
            "changed_rows": changed_rows, "rem_filtered_path": rem_filtered_path,
            "new_test_path": new_test_path, "update_test_path": update_test_path}

def remove_test_cases(state):
    """
    Removes test cases corresponding to requirements that have been removed.

    Args:
        state (dict): Contains the removed sections (`removed_sections`) and path to the old test cases file (`old_test_cases_file_path`).

    Returns:
        dict: Updated state with the path to the filtered test cases file (`rem_filtered_path`).
    """
    print("---CHECKING IF REQUIREMENTS WERE REMOVED---")
    # Get the removed sections list
    removed_sections = state['removed_sections']
    # Check if there are no removed sections
    if removed_sections == []:
        print("---DECISION: NO REQUIREMENTS WERE REMOVED---")
        return {"rem_filtered_path": state['old_test_cases_file_path']}
    else:
        print("---DECISION: DELETING TEST CASES FOR THE REMOVED REQUIREMENTS---")
        # Getting the ids to remove 
        removed_ids = []
        for sec in removed_sections:
            removed_ids.append(int(sec['Req ID']))

        # Get the path of the test_cases file
        old_test_cases_file_path = state['old_test_cases_file_path']
        df = pd.read_csv(old_test_cases_file_path)
        filtered_df = df[~df['requirement_id'].isin(removed_ids)]
        rem_filtered_path = './update/removed_tests.csv'
        filtered_df.to_csv(rem_filtered_path, index=False)
        print(f"Filtered file saved at: {rem_filtered_path}")
        return {"rem_filtered_path": rem_filtered_path}
    return 

def new_test_cases(state):
    """
    Generates test cases for newly added requirements.

    Args:
        state (dict): Contains the new sections (`new_sections`).

    Returns:
        dict: Updated state with the path to the newly generated test cases file (`new_test_path`).
    """
    print("---CHECKING IF THERE ARE NEW REQUIREMENTS---")
    # Get the new sections list
    new_sections = state['new_sections']
    if new_sections == []:
        print("---DECISION: NO NEW SECTIONS WERE FOUND---")
        return 
    else:
        # Importing here to prevent circular import error
        from graph import create_graph_workflow
        print("---DECISION: NEW REQUIREMENTS WERE FOUND, GENERATING NEW TEST CASES---")
        # Convert the list of dicts to a df for processing by the generate test cases graph
        new_sections_df = pd.DataFrame(new_sections)
        new_sections_df.to_csv('./update/new_req.csv', index=False)
        # Create the app and run the graph
        app = create_graph_workflow()
        new_test_path = './update/new_test_cases.csv'
        # Define the inputs  
        inputs = {"csv_file_path": './update/new_req.csv', 'output_file_path': new_test_path}
        # Run the graph
        for output in app.stream(inputs):
            for key, value in output.items():
                # Node
                pass
        print(f"New test cases file saved at: {new_test_path}")
        # Delete the requirements file as it is no longer needed
        os.remove('./update/new_req.csv')
        return {"new_test_path": new_test_path}

def update_test_cases(state):
    """
    Updates test cases for modified requirements.

    Args:
        state (dict): Contains the changed rows (`changed_rows`), old test cases file path (`old_test_cases_file_path`), and new requirements file path (`new_csv_file_path`).

    Returns:
        dict: Updated state with the path to the updated test cases file (`update_test_path`).
    """
    print("---CHECKING IF REQUIREMENTS WERE UPDATED---")
    changed_rows = state['changed_rows']
    if changed_rows == []:
        print('---DECSION: NO REQUIREMENTS WERE UPDATED---')
        return
    else:
        print("---DECISION: UPDATING REQUIREMENTS---")
        # get the old test_cases_csv file
        old_test_cases_file_path = state['old_test_cases_file_path']
        data = pd.read_csv(old_test_cases_file_path)
        # Get the ids of the changed requirements
        changed_ids = []
        for row in changed_rows:
            changed_ids.append(int(row['Req ID']))
        tests_to_change = data[data['requirement_id'].isin(changed_ids)]

        # Convert this to string
        res = df_to_custom_string(tests_to_change)
        # Convert changed reqs to string
        changed_req = changed_rows_to_str(changed_rows)

        # Get the type of generation
        type_gen = state['type_gen']

        # Get the output file path
        update_test_path = './update/updated_tests.csv'
        new_csv_file_path = state['new_csv_file_path']

        if type_gen == 'general':
            print('---GENERATING ALL TEST CASES AT ONCE---')
            general_res = general_generator.invoke({"changed_req": changed_req, "old_test_cases": res})
            generate_gen_updated_test_case_csv(general_res.test_cases, 
                                               output_file=update_test_path, 
                                               req_desc_file=new_csv_file_path)
            return {"update_test_path": update_test_path}
        else:
            res = pos_update_generator.invoke({"changed_req": changed_req, "old_test_cases": res})
            neg_res = neg_update_generator.invoke({"changed_req": changed_req, "old_test_cases": res})
            edge_res = edge_update_generator.invoke({"changed_req": changed_req, "old_test_cases": res})

            generate_updated_test_case_csv(res.test_cases, neg_res.test_cases, 
                                           edge_res.test_cases, output_file=update_test_path, 
                                           req_desc_file=new_csv_file_path)
            return {"update_test_path": update_test_path}
        
def finalize_csv(state):
    """
    Finalizes the output by combining updated, removed, and new test cases into a single CSV file.

    Args:
        state (dict): Contains paths to intermediate files for removed, new, and updated test cases.

    Returns:
        dict: Updated state with the path to the final output file.
    """
    # Get the file paths of all three operations
    rem_filtered_path = state['rem_filtered_path']
    new_test_path = state['new_test_path']
    update_test_path = state['update_test_path']
    file_paths = [rem_filtered_path, new_test_path, update_test_path]

    if all(path is None for path in file_paths):
        print("No Changes were required.")
        return 
    else:
        dfs = []
        for path in file_paths:
            if path is not None:
                print(f"Processing file {path}")
                df = pd.read_csv(path)
                dfs.append(df)
                # Delete the csv file
                os.remove(path)
                print(f"File deleted: {path}")
        combined_df = pd.concat(dfs, ignore_index=True)
        # updated_file_path = './update/updated_test_cases.csv'
        output_file_path = state['output_file_path']
        combined_df.to_csv(output_file_path, index=False)
        print(f"Files combined and saved as: {output_file_path}")
        return {"output_file_path": output_file_path}