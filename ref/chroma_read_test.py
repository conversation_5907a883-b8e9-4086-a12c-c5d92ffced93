# Imports
from dotenv import load_dotenv
import os
from langchain_chroma import Chroma
from langchain_openai import AzureOpenAIEmbeddings
import os
from langchain_openai import AzureOpenAIEmbeddings

# Load the .env file
load_dotenv()
os.environ['AZURE_OPENAI_ENDPOINT'] = os.getenv("AZURE_OPENAI_EMBEDDING_ENDPOINT")
os.environ['OPENAI_API_VERSION'] = '2023-05-15'
embeddings = AzureOpenAIEmbeddings(model='text-embedding-3-small')

vector_store = Chroma(
    embedding_function=embeddings,
    persist_directory='./chroma_db_new_test',
    collection_name='bts-slc-db'
)
 
id_list = vector_store.get()['ids'] 
print(id_list)
assert id_list, "List is empty"